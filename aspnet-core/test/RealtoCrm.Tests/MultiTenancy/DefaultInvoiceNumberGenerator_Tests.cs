using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Domain.Repositories;
using Abp.Timing;
using RealtoCrm.MultiTenancy.Accounting;
using RealtoCrm.Test.Base;
using Shouldly;

namespace RealtoCrm.Tests.MultiTenancy;

// ReSharper disable once InconsistentNaming
public class DefaultInvoiceNumberGenerator_Tests : AppTestBase
{
    private readonly IInvoiceNumberGenerator invoiceNumberGenerator;
    private readonly IRepository<Invoice> invoiceRepository;

    public DefaultInvoiceNumberGenerator_Tests()
    {
        this.invoiceNumberGenerator = this.Resolve<IInvoiceNumberGenerator>();
        this.invoiceRepository = this.Resolve<IRepository<Invoice>>();
    }

    [MultiTenantFact]
    public async Task Should_Not_Generate_Same_Invoice_Number()
    {
        var invoiceCountToGenerate = 100;
        var invoiceNumbers = new List<string>();

        for (var i = 0; i < invoiceCountToGenerate; i++)
        {
            var invoiceNo = await this.invoiceNumberGenerator.GetNewInvoiceNumber();
            invoiceNo.ShouldNotBeNullOrEmpty();
            invoiceNo.Length.ShouldBe(11);//Should be YYYYMM00001

            await this.invoiceRepository.InsertAsync(new Invoice
            {
                InvoiceNo = invoiceNo,
                InvoiceDate = Clock.Now,
                TenantAddress = "USA",
                TenantLegalName = "AspNet Zero",
                TenantTaxNo = "*********"
            });

            invoiceNumbers.ShouldNotContain(invNo => invNo == invoiceNo);
            invoiceNumbers.Add(invoiceNo);
        }

        invoiceNumbers.Count.ShouldBe(invoiceCountToGenerate);
    }

    [MultiTenantFact]
    public async Task Should_Start_Over_InvoiceNo_When_Month_Changes()
    {
        await this.invoiceRepository.InsertAsync(new Invoice
        {
            InvoiceNo = Clock.Now.Year + (Clock.Now.Month - 1).ToString("00") + "55555",
            InvoiceDate = Clock.Now,
            TenantAddress = "USA",
            TenantLegalName = "AspNet Zero",
            TenantTaxNo = "*********"
        });

        var invoiceNo = await this.invoiceNumberGenerator.GetNewInvoiceNumber();
        invoiceNo.ShouldBe(Clock.Now.Year + Clock.Now.Month.ToString("00") + "00001");
    }

    [MultiTenantFact]
    public async Task Should_Start_Over_InvoiceNo_When_Year_Changes()
    {
        await this.invoiceRepository.InsertAsync(new Invoice
        {
            InvoiceNo = (Clock.Now.Year - 1) + (Clock.Now.Month).ToString("00") + "55555",
            InvoiceDate = Clock.Now,
            TenantAddress = "USA",
            TenantLegalName = "AspNet Zero",
            TenantTaxNo = "*********"
        });

        var invoiceNo = await this.invoiceNumberGenerator.GetNewInvoiceNumber();
        invoiceNo.ShouldBe(Clock.Now.Year + (Clock.Now.Month).ToString("00") + "00001");
    }

    [MultiTenantFact]
    public async Task Should_Handle_When_Invoice_Number_Exceeds_Defined_Range()
    {
        await this.invoiceRepository.InsertAsync(new Invoice
        {
            InvoiceNo = Clock.Now.Year + Clock.Now.Month.ToString("00") + "99999",
            InvoiceDate = Clock.Now,
            TenantAddress = "USA",
            TenantLegalName = "AspNet Zero",
            TenantTaxNo = "*********"
        });

        var invoiceNo = await this.invoiceNumberGenerator.GetNewInvoiceNumber();
        invoiceNo.ShouldBe(Clock.Now.Year + (Clock.Now.Month).ToString("00") + "100000");

        await this.invoiceRepository.InsertAsync(new Invoice
        {
            InvoiceNo = invoiceNo,
            InvoiceDate = Clock.Now,
            TenantAddress = "USA",
            TenantLegalName = "AspNet Zero",
            TenantTaxNo = "*********"
        });

        invoiceNo = await this.invoiceNumberGenerator.GetNewInvoiceNumber();
        invoiceNo.ShouldBe(Clock.Now.Year + (Clock.Now.Month).ToString("00") + "100001");
    }
}