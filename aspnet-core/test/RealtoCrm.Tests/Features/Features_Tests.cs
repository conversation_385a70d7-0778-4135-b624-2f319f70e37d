using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Localization;
using Abp.MultiTenancy;
using Abp.UI;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Authorization.Users.Dto;
using RealtoCrm.Editions;
using RealtoCrm.Editions.Dto;
using RealtoCrm.Features;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.Features;

// ReSharper disable once InconsistentNaming
public class Features_Tests : AppTestBase
{
    private readonly IEditionAppService editionAppService;
    private readonly IUserAppService userAppService;
    private readonly ILocalizationManager localizationManager;

    public Features_Tests()
    {
        this.LoginAsHostAdmin();
        this.editionAppService = this.Resolve<IEditionAppService>();
        this.userAppService = this.Resolve<IUserAppService>();
        this.localizationManager = this.Resolve<ILocalizationManager>();
    }

    [MultiTenantFact]
    public async Task Should_Not_Create_User_More_Than_Allowed_Count()
    {
        //Getting edition for edit
        var output = await this.editionAppService.GetEditionForEdit(new NullableIdDto(null));

        //Changing a sample feature value
        var maxUserCountFeature = output.FeatureValues.FirstOrDefault(f => f.Name == AppFeatures.MaxUserCount);
        if (maxUserCountFeature != null)
        {
            maxUserCountFeature.Value = "2";
        }

        await this.editionAppService.CreateEdition(
            new CreateEditionDto
            {
                Edition = new EditionCreateDto
                {
                    DisplayName = "Premium Edition"
                },
                FeatureValues = output.FeatureValues
            });


        var premiumEdition = (await this.editionAppService.GetEditions()).Items.FirstOrDefault(e => e.DisplayName == "Premium Edition");
        premiumEdition.ShouldNotBeNull();

        await this.UsingDbContextAsync(async context =>
        {
            var tenant = await context.Tenants.SingleAsync(t => t.TenancyName == AbpTenantBase.DefaultTenantName);
            tenant.EditionId = premiumEdition.Id;

            await context.SaveChangesAsync();
        });

        this.LoginAsDefaultTenantAdmin();

        // This is second user (first is tenant admin)
        await this.userAppService.CreateOrUpdateUser(
            new CreateOrUpdateUserInput
            {
                User = new UserEditDto
                {
                    EmailAddress = "<EMAIL>",
                    UserName = "johnnash",
                    Password = "123qwE*"
                },
                AssignedRoleNames = new string[] { }
            });

        //Act
        var exception = await Assert.ThrowsAsync<UserFriendlyException>(
            async () =>
                await this.userAppService.CreateOrUpdateUser(
                    new CreateOrUpdateUserInput
                    {
                        User = new UserEditDto
                        {
                            EmailAddress = "<EMAIL>",
                            UserName = "alirizaadiyahsi",
                            Password = "123qwE*"
                        },
                        AssignedRoleNames = new string[] { }
                    })
        );

        exception.Message.ShouldContain(this.localizationManager.GetString(RealtoCrmConsts.LocalizationSourceName, "MaximumUserCount_Error_Message"));
    }
}