using System.Threading.Tasks;
using Abp.Configuration;
using RealtoCrm.Configuration;
using RealtoCrm.Configuration.Tenants;
using Shouldly;
using Xunit;

namespace RealtoCrm.Tests.Configuration.Tenants;

// ReSharper disable once InconsistentNaming
public class TenantSettingsAppService_Tests : AppTestBase
{
    private readonly ITenantSettingsAppService tenantSettingsAppService;
    private readonly ISettingManager settingManager;

    public TenantSettingsAppService_Tests()
    {
        this.tenantSettingsAppService = this.Resolve<ITenantSettingsAppService>();
        this.settingManager = this.Resolve<ISettingManager>();

        this.LoginAsDefaultTenantAdmin();
        this.InitializeTestSettings();
    }

    private void InitializeTestSettings()
    {
        this.settingManager.ChangeSettingForApplicationAsync(AppSettings.UserManagement.AllowSelfRegistration, "true");
        this.settingManager.ChangeSettingForApplicationAsync(AppSettings.UserManagement.IsNewRegisteredUserActiveByDefault, "false");
    }

    [Fact(Skip = "Getting exception: Abp.Authorization.AbpAuthorizationException : Required permissions are not granted. At least one of these permissions must be granted: Settings")]
    public async Task Should_Change_UserManagement_Settings()
    {
        //Get and check current settings

        //Act
        var settings = await this.tenantSettingsAppService.GetAllSettings();

        //Assert
        settings.UserManagement.AllowSelfRegistration.ShouldBe(true);
        settings.UserManagement.IsNewRegisteredUserActiveByDefault.ShouldBe(false);
        settings.UserManagement.UseCaptchaOnRegistration.ShouldBe(true);

        //Change and save settings

        //Arrange
        settings.UserManagement.AllowSelfRegistration = true;
        settings.UserManagement.IsNewRegisteredUserActiveByDefault = true;
        settings.UserManagement.UseCaptchaOnRegistration = false;

        await this.tenantSettingsAppService.UpdateAllSettings(settings);

        //Assert
        this.settingManager.GetSettingValue<bool>(AppSettings.UserManagement.AllowSelfRegistration).ShouldBe(true);
        this.settingManager.GetSettingValue<bool>(AppSettings.UserManagement.IsNewRegisteredUserActiveByDefault).ShouldBe(true);
        this.settingManager.GetSettingValue<bool>(AppSettings.UserManagement.UseCaptchaOnRegistration).ShouldBe(false);
    }
}