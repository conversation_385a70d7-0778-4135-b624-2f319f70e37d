using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class ModifyEmployeeMappingEaIdProperty : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "EaId",
                table: "EmployeeMappings",
                type: "text",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "EaId",
                table: "EmployeeMappings",
                type: "integer",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
