using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class HasOpticToOfferSearchProject : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "HasOptic",
                table: "SearchDetails",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "HasOptic",
                table: "ProjectDetails",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "HasOptic",
                table: "OfferDetails",
                type: "boolean",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HasOptic",
                table: "SearchDetails");

            migrationBuilder.DropColumn(
                name: "HasOptic",
                table: "ProjectDetails");

            migrationBuilder.DropColumn(
                name: "HasOptic",
                table: "OfferDetails");
        }
    }
}
