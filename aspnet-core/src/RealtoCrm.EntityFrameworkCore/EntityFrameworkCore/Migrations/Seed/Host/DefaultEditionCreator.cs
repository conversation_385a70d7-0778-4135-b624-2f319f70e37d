namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.Host;

using System.Linq;
using Abp.Application.Features;
using Editions;
using Features;
using Microsoft.EntityFrameworkCore;

public class DefaultEditionCreator(RealtoCrmDbContext context)
{
    public void Create()
    {
        this.CreateEditions();
    }

    private void CreateEditions()
    {
        var defaultEdition = context.Editions.IgnoreQueryFilters().FirstOrDefault(e => e.Name == EditionManager.DefaultEditionName);
        if (defaultEdition == null)
        {
            defaultEdition = new SubscribableEdition { Name = EditionManager.DefaultEditionName, DisplayName = EditionManager.DefaultEditionName };
            context.Editions.Add(defaultEdition);
            context.SaveChanges();

            /* Add desired features to the standard edition, if wanted... */
        }

        if (defaultEdition.Id > 0)
        {
            this.CreateFeatureIfNotExists(defaultEdition.Id, AppFeatures.ChatFeature, true);
            this.CreateFeatureIfNotExists(defaultEdition.Id, AppFeatures.TenantToTenantChatFeature, true);
            this.CreateFeatureIfNotExists(defaultEdition.Id, AppFeatures.TenantToHostChatFeature, true);
        }
    }

    private void CreateFeatureIfNotExists(int editionId, string featureName, bool isEnabled)
    {
        var defaultEditionChatFeature = context.EditionFeatureSettings.IgnoreQueryFilters()
            .FirstOrDefault(ef => ef.EditionId == editionId && ef.Name == featureName);

        if (defaultEditionChatFeature == null)
        {
            context.EditionFeatureSettings.Add(new EditionFeatureSetting
            {
                Name = featureName,
                Value = isEnabled.ToString().ToLower(),
                EditionId = editionId
            });
        }
    }
}