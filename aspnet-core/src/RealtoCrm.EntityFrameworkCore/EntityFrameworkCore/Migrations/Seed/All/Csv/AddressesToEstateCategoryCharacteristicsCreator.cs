namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Csv;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using AddressesEstateCharacteristics;
using Base;
using CsvHelper;
using Estates;
using Microsoft.EntityFrameworkCore;
using MultiTenancy;

public class AddressesToEstateCategoryCharacteristicsCreator(DbContext context)
    : BaseCsvDbBuilder<AddressToEstateCategoryCharacteristic>(context)
{
    private const string EstateCategoryNameColumnName = "EstateCategoryName";
    private const string TenantNameColumnName = "TenantName";
    protected override string FileName => "addresses_to_estate_category_settings.csv";

    private const string TenantIdPropertyName = nameof(AddressToEstateCategoryCharacteristic.TenantId);
    private const string EstateCategoryIdColumnName = nameof(AddressToEstateCategoryCharacteristic.CategoryId);

    protected override Expression<Func<AddressToEstateCategoryCharacteristic, string>> EqualityMembersSelector
        => x => string.Join(";", x.TenantId.ToString(), x.PropertyName.ToString());

    private IDictionary<string, int> TenantsMap { get; set; }
    private IDictionary<string, int> EstateCategoriesMap { get; set; }

    private static bool IsTrue(object value) =>
        value.ToString() == "t" || value.ToString() == "true" || value.ToString() == "1";

    protected override List<string> CsvColumnNames =>
    [
        nameof(AddressToEstateCategoryCharacteristic.PropertyName),
        nameof(AddressToEstateCategoryCharacteristic.IsVisible),
        EstateCategoryNameColumnName,
        TenantNameColumnName,
    ];

    protected override async Task PreloadData()
    {
        this.TenantsMap = (
                await context.Set<Tenant>()
                    .ToListAsync()
            )
            .ToDictionary(
                t => t.Name,
                t => t.Id
            );

        this.EstateCategoriesMap = (
                await context.Set<EstateCategory>()
                    .ToListAsync()
            )
            .ToDictionary(
                ec => ec.Name,
                ec => ec.Id);

        await base.PreloadData();
    }

    protected override string GetPropertyName(string columnName)
    {
        var result = columnName switch
        {
            TenantNameColumnName => TenantIdPropertyName,
            EstateCategoryNameColumnName => EstateCategoryIdColumnName,
            var _ => base.GetPropertyName(columnName)
        };

        return result;
    }

    protected override object ParseValue(string columnName, object originalValue, CsvReader csv)
    {
        var result = columnName switch
        {
            TenantNameColumnName => this.TenantsMap[originalValue.ToString()],
            EstateCategoryNameColumnName => this.EstateCategoriesMap[originalValue.ToString()],
            nameof(AddressToEstateCategoryCharacteristic.IsVisible) => IsTrue(originalValue),
            var _ => base.ParseValue(columnName, originalValue, csv),
        };

        return result;
    }
}