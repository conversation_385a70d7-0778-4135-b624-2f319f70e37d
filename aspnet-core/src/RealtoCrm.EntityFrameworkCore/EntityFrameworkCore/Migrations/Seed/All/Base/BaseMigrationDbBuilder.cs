namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Extensions;

public abstract class BaseMigrationDbBuilder<T>(DbContext dbContext)
    : BaseDbBuilder
    where T : class
{
    protected abstract IEnumerable<IEnumerable<SeedValue<T>>> ObjectData { get; }

    protected abstract Expression<Func<T, string>> EqualityMembersSelector { get; }

    protected virtual IEnumerable<IEnumerable<SeedValue<T>>> GetObjectsData()
        => this.ObjectData;

    protected virtual bool DoBatchSaveChanges => false;

    protected virtual async Task<IEnumerable<string>> GetExistingEntities()
        => await dbContext.Set<T>()
            .IgnoreQueryFilters()
            .Select(this.EqualityMembersSelector)
            .ToListAsync();

    protected override async Task InsertAllAsync()
    {
        var objectCreateTasks = this.GetObjectsData()
            .Select(this.CreateEntity);

        var existingEntities = await this.GetExistingEntities()
            .ToSetAsync();

        var selectEqualityMembers = this.EqualityMembersSelector.Compile();

        foreach (var objectCreateTask in objectCreateTasks)
        {
            var entity = await objectCreateTask;
            if (existingEntities.Contains(selectEqualityMembers(entity)))
            {
                continue;
            }

            var decorateObj = await this.DecorateEntityAsync(entity);
            
            await this.InsertAsync(decorateObj);
            if (!this.DoBatchSaveChanges)
            {
                await dbContext.SaveChangesAsync();
            }
        }

        await dbContext.SaveChangesAsync();
    }

    protected virtual async Task InsertAsync(T entity)
        => await dbContext.AddAsync(entity);

    protected virtual Task<T> DecorateEntityAsync(T entity)
        => Task.FromResult(entity);

    protected virtual Task<T> CreateEntity(IEnumerable<SeedValue<T>> data)
    {
        var instance = Activator.CreateInstance<T>();
        foreach (var property in data)
        {
            var propertyInfo = instance.GetType().GetProperty(property.Name);
            var propertyValue = property.FindFunc != null
                ? property.FindFunc(dbContext)
                : property.Value;
            propertyInfo!.SetValue(instance, propertyValue);
        }

        return Task.FromResult(instance);
    }
}