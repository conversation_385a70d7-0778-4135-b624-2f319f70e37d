namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Nomenclatures;

public class TitlesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<Title>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<Title>>> ObjectData
        =>
        [
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Госпожа",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Господин",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Доцент",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Доктор",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Професор",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Адвокат",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Инженер",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Нотариус",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Архитект",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Посланик",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Академик",
                },
            ],
            [
                new SeedValue<Title>
                {
                    Name = nameof(Title.Name),
                    Value = "Лорд",
                },
            ],
        ];
}