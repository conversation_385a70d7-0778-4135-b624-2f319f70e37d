namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Base;
using RealtoCrm.Nomenclatures;
using static CosherConsts.ClientTypes;

public class ClientTypesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<ClientType>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<ClientType>>> ObjectData
        =>
        [
            [
                new SeedValue<ClientType>
                {
                    Name = nameof(ClientType.Name),
                    Value = PersonalClientTypeName,
                }
            ],
            [
                new SeedValue<ClientType>
                {
                    Name = nameof(ClientType.Name),
                    Value = LegalClientTypeName,
                }
            ],
        ];
};