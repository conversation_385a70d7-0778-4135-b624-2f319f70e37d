using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Base;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

public class FloorPreferencesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<FloorPreference>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<FloorPreference>>> ObjectData
        =>
        [
            [
                new SeedValue<FloorPreference>
                {
                    Name = nameof(FloorPreference.Name),
                    Value = "Без първи",
                }
            ],
            [
                new SeedValue<FloorPreference>
                {
                    Name = nameof(FloorPreference.Name),
                    Value = "Без последен",
                }
            ],
        ];
};