using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class AddClientTypeIntoRelatedClientTypes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ClientTypeId",
                table: "RelatedClientTypes",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_RelatedClientTypes_ClientTypeId",
                table: "RelatedClientTypes",
                column: "ClientTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_RelatedClientTypes_ClientTypes_ClientTypeId",
                table: "RelatedClientTypes",
                column: "ClientTypeId",
                principalTable: "ClientTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RelatedClientTypes_ClientTypes_ClientTypeId",
                table: "RelatedClientTypes");

            migrationBuilder.DropIndex(
                name: "IX_RelatedClientTypes_ClientTypeId",
                table: "RelatedClientTypes");

            migrationBuilder.DropColumn(
                name: "ClientTypeId",
                table: "RelatedClientTypes");
        }
    }
}
