using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RealtoCrm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class LinkedOffers : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "LinkedOfferId",
                table: "Offers",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Offers_LinkedOfferId",
                table: "Offers",
                column: "LinkedOfferId");

            migrationBuilder.AddForeignKey(
                name: "FK_Offers_Offers_LinkedOfferId",
                table: "Offers",
                column: "LinkedOfferId",
                principalTable: "Offers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Offers_Offers_LinkedOfferId",
                table: "Offers");

            migrationBuilder.DropIndex(
                name: "IX_Offers_LinkedOfferId",
                table: "Offers");

            migrationBuilder.DropColumn(
                name: "LinkedOfferId",
                table: "Offers");
        }
    }
}
