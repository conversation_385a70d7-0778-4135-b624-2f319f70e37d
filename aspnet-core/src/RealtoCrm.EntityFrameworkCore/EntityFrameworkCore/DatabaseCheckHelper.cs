using Abp.Dependency;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Abp.Extensions;
using Microsoft.EntityFrameworkCore;

namespace RealtoCrm.EntityFrameworkCore;

public class DatabaseCheckHelper(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IUnitOfWorkManager unitOfWorkManager) : ITransientDependency
{
    public bool Exist(string connectionString)
    {
        if (connectionString.IsNullOrEmpty())
        {
            //connectionString is null for unit tests
            return true;
        }

        try
        {
            using (var uow =unitOfWorkManager.Begin())
            {
                // Switching to host is necessary for single tenant mode.
                using (unitOfWorkManager.Current.SetTenantId(null))
                {
                    dbContextProvider.GetDbContext().Database.OpenConnection();
                    uow.Complete();
                }
            }
        }
        catch
        {
            return false;
        }

        return true;
    }
}