using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Abp.EntityFrameworkCore.Repositories;

namespace RealtoCrm.EntityFrameworkCore.Repositories;

/// <summary>
/// Base class for custom repositories of the application.
/// </summary>
/// <typeparam name="TEntity">Entity type</typeparam>
/// <typeparam name="TPrimaryKey">Primary key type of the entity</typeparam>
public abstract class RealtoCrmRepositoryBase<TEntity, TPrimaryKey>(IDbContextProvider<RealtoCrmDbContext> dbContextProvider) : EfCoreRepositoryBase<RealtoCrmDbContext, TEntity, TPrimaryKey>(dbContextProvider)
    where TEntity : class, IEntity<TPrimaryKey>
{
    //add your common methods for all repositories
}

/// <summary>
/// Base class for custom repositories of the application.
/// This is a shortcut of <see cref="RealtoCrmRepositoryBase{TEntity,TPrimaryKey}"/> for <see cref="int"/> primary key.
/// </summary>
/// <typeparam name="TEntity">Entity type</typeparam>
public abstract class RealtoCrmRepositoryBase<TEntity>(IDbContextProvider<RealtoCrmDbContext> dbContextProvider) : RealtoCrmRepositoryBase<TEntity, int>(dbContextProvider)
    where TEntity : class, IEntity<int>
{
    //do not add any method here, add to the class above (since this inherits it)!!!
}