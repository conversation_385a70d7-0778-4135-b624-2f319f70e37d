namespace RealtoCrm.EntityFrameworkCore.Configurations.Deposits;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Deposits;

internal class DepositConfiguration : IEntityTypeConfiguration<Deposit>
{
    public void Configure(EntityTypeBuilder<Deposit> builder)
    {
        builder
            .HasKey(d => d.Id);

        builder
            .Property(d => d.NotaryDate)
            .IsRequired();

        builder
            .Property(d => d.SigningDate)
            .IsRequired();

        builder
            .Property(d => d.SigningEndDate)
            .IsRequired();

        builder
            .OwnsMoney(d => d.Amount);

        builder
            .OwnsMoney(d => d.OfferedPrice);

        builder
            .Property(d => d.TransferredToOwner)
            .HasDefaultValue(false);

        builder
            .HasOne(d => d.DepositStatus)
            .WithMany(ds => ds.Deposits)
            .HasForeignKey(d => d.DepositStatusId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(d => d.Match)
            .WithMany(m => m.Deposits)
            .HasForeignKey(d => d.MatchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(d => d.Search)
            .WithMany(s => s.Deposits)
            .HasForeignKey(d => d.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(d => d.SearchClient)
            .WithMany()
            .HasForeignKey(d => d.SearchClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(d => d.SearchEmployee)
            .WithMany(e => e.EmployeeSearchDeposits)
            .HasForeignKey(d => d.SearchEmployeeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(d => d.Offer)
            .WithMany(o => o.Deposits)
            .HasForeignKey(d => d.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(d => d.OfferClient)
            .WithMany()
            .HasForeignKey(d => d.OfferClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(d => d.OfferEmployee)
            .WithMany(e => e.EmployeeOfferDeposits)
            .HasForeignKey(d => d.OfferEmployeeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}