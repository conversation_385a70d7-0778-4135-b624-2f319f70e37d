namespace RealtoCrm.EntityFrameworkCore.Configurations.Companies;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Companies;

internal class WorkingTimeConfiguration : IEntityTypeConfiguration<WorkingTime>
{
    public void Configure(EntityTypeBuilder<WorkingTime> builder)
    {
        builder
            .HasKey(wt => wt.Id);

        builder
            .Property(wt => wt.DayOfWeek)
            .IsRequired();

        builder
            .Property(wt => wt.Start)
            .IsRequired();

        builder
            .Property(wt => wt.End)
            .IsRequired();

        builder
            .Property(wt => wt.Type)
            .IsRequired();

        builder
            .HasOne(wt => wt.Office)
            .WithMany(o => o.WorkingTimes)
            .HasForeignKey(wt => wt.OfficeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}