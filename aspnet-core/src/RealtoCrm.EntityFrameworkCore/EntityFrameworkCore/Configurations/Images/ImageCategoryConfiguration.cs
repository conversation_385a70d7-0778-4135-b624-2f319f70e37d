namespace RealtoCrm.EntityFrameworkCore.Configurations.Images;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Images;

internal class ImageCategoryConfiguration : IEntityTypeConfiguration<ImageCategory>
{
    public void Configure(EntityTypeBuilder<ImageCategory> builder)
    {
        builder
            .HasKey(c => c.Id);

        builder
            .Property(c => c.Name)
            .IsRequired();
    }
}