using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.EntityFrameworkCore.Extensions;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

public class ExternalAgencyConfiguration : IEntityTypeConfiguration<ExternalAgency>
{
    public void Configure(EntityTypeBuilder<ExternalAgency> builder) =>
        builder
            .HasNomenclature();
}