namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.EntityFrameworkCore.Extensions;
using RealtoCrm.Nomenclatures;

public class RelatedClientTypeConfiguration : IEntityTypeConfiguration<RelatedClientType>
{
    public void Configure(EntityTypeBuilder<RelatedClientType> builder)
    {
        builder.HasNomenclature();
        
        builder.HasOne(rct => rct.ClientType)
            .WithMany(ct => ct.RelatedClientTypes)
            .HasForeignKey(rct => rct.ClientTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}