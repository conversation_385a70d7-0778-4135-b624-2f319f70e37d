namespace RealtoCrm.EntityFrameworkCore.Configurations.Nomenclatures;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Nomenclatures;

internal class FacingDirectionConfiguration : IEntityTypeConfiguration<FacingDirection>
{
    public void Configure(EntityTypeBuilder<FacingDirection> builder)
        => builder.HasNomenclature();
}