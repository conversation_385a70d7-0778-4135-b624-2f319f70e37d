namespace RealtoCrm.EntityFrameworkCore.Configurations.Estates;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Estates;

internal class EstateFileConfiguration : IEntityTypeConfiguration<EstateFile>
{
    public void Configure(EntityTypeBuilder<EstateFile> builder)
    {
        builder
            .HasKey(ef => new { ef.EstateId, ef.FileId });

        builder
            .HasOne(ef => ef.Estate)
            .WithMany(e => e.EstatesFiles)
            .HasForeignKey(ef => ef.EstateId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(ef => ef.File)
            .WithMany(f => f.EstatesFiles)
            .HasForeignKey(ef => ef.FileId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}