namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;

internal class OfferTaskConfiguration : IEntityTypeConfiguration<OfferTask>
{
    public void Configure(EntityTypeBuilder<OfferTask> builder)
    {
        builder
            .HasKey(ot => new { ot.OfferId, ot.TaskId });

        builder
            .HasOne(ot => ot.Offer)
            .WithMany(o => o.OffersTasks)
            .HasForeignKey(ot => ot.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(ot => ot.Task)
            .WithMany(t => t.OffersTasks)
            .HasForeignKey(ot => ot.TaskId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}