namespace RealtoCrm.EntityFrameworkCore.Configurations.Authorization;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Authorization.Delegation;

internal class UserDelegationConfiguration : IEntityTypeConfiguration<UserDelegation>
{
    public void Configure(EntityTypeBuilder<UserDelegation> builder)
    {
        builder
            .HasIndex(ud => new { ud.TenantId, ud.SourceUserId });

        builder
            .HasIndex(ud => new { ud.TenantId, ud.TargetUserId });
    }
}