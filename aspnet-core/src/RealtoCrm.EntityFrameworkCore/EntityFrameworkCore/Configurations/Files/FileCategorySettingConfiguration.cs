namespace RealtoCrm.EntityFrameworkCore.Configurations.Files;

using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Files;

internal class FileCategorySettingConfiguration : IEntityTypeConfiguration<FileCategorySetting>
{
    public void Configure(EntityTypeBuilder<FileCategorySetting> builder)
    {
        builder
            .HasKey(f => f.Id);
        
        builder
            .Property(f => f.ObjectType)
            .IsRequired();

        builder
            .HasOne<FileCategory>(f => f.Category)
            .WithMany(f => f.FileCategorySettings)
            .HasForeignKey(f => f.CategoryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}