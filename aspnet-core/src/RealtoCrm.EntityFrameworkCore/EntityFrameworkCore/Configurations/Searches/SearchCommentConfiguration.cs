namespace RealtoCrm.EntityFrameworkCore.Configurations.Searches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Searches;

internal class SearchCommentConfiguration : IEntityTypeConfiguration<SearchComment>
{
    public void Configure(EntityTypeBuilder<SearchComment> builder)
    {
        builder
            .HasKey(sc => new { sc.SearchId, sc.CommentId });

        builder
            .HasOne(sc => sc.Search)
            .WithMany(s => s.SearchesComments)
            .HasForeignKey(sc => sc.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(sc => sc.Comment)
            .WithMany(c => c.SearchesComments)
            .HasForeignKey(sc => sc.CommentId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}