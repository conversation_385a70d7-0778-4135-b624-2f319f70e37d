namespace RealtoCrm.EntityFrameworkCore.Configurations.Matches;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Matches;

internal class MatchConfiguration : IEntityTypeConfiguration<Match>
{
    public void Configure(EntityTypeBuilder<Match> builder)
    {
        builder
            .<PERSON><PERSON>ey(m => m.Id);

        builder
            .<PERSON>One(m => m.Status)
            .WithMany(s => s.Matches)
            .HasForeignKey(m => m.StatusId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(m => m.Offer)
            .WithMany(s => s.Matches)
            .HasForeignKey(m => m.OfferId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(m => m.Search)
            .WithMany(s => s.Matches)
            .HasForeignKey(m => m.SearchId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}