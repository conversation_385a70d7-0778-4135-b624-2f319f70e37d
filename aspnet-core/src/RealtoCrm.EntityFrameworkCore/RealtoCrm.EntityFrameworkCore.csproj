<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <RootNamespace>RealtoCrm</RootNamespace>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Abp.EntityFrameworkCore.EFPlus" Version="9.0.0" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.2" />
    <PackageReference Include="OpenIddict.EntityFrameworkCore" Version="4.10.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RealtoCrm.Application.Shared\RealtoCrm.Application.Shared.csproj" />
    <ProjectReference Include="..\RealtoCrm.Core\RealtoCrm.Core.csproj" />
  </ItemGroup>
</Project>