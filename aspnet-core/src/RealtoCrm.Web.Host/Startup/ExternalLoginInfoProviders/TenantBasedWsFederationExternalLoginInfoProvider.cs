using System.Collections.Generic;
using Abp.AspNetZeroCore.Web.Authentication.External;
using Abp.AspNetZeroCore.Web.Authentication.External.Microsoft;
using Abp.AspNetZeroCore.Web.Authentication.External.WsFederation;
using Abp.Configuration;
using Abp.Dependency;
using Abp.Extensions;
using Abp.Json;
using Abp.Runtime.Caching;
using Abp.Runtime.Session;
using RealtoCrm.Authentication;
using RealtoCrm.Configuration;

namespace RealtoCrm.Web.Startup.ExternalLoginInfoProviders;

public class TenantBasedWsFederationExternalLoginInfoProvider(
    ISettingManager settingManager,
    IAbpSession abpSession,
    ICacheManager cacheManager) : TenantBasedExternalLoginInfoProviderBase(abpSession, cacheManager),
    ISingletonDependency
{
    private readonly IAbpSession abpSession = abpSession;
    public override string Name { get; } = WsFederationAuthProviderApi.Name;

    private ExternalLoginProviderInfo CreateExternalLoginInfo(WsFederationExternalLoginProviderSettings settings)
    {
        var mappingSettings = settingManager.GetSettingValue(AppSettings.ExternalLoginProvider.WsFederationMappedClaims);
        var jsonClaimMappings = mappingSettings.FromJsonString<List<JsonClaimMap>>();

        return new ExternalLoginProviderInfo(
            WsFederationAuthProviderApi.Name,
            settings.ClientId,
            "",
            typeof(WsFederationAuthProviderApi),
            new Dictionary<string, string>
            {
                {"Tenant", settings.Tenant},
                {"MetaDataAddress", settings.MetaDataAddress},
                {"Authority", settings.Authority}
            },
            jsonClaimMappings
        );
    }

    protected override bool TenantHasSettings()
    {
        var settingValue = settingManager.GetSettingValueForTenant(AppSettings.ExternalLoginProvider.Tenant.WsFederation, this.abpSession.TenantId.Value);
        return !settingValue.IsNullOrWhiteSpace();
    }

    protected override ExternalLoginProviderInfo GetTenantInformation()
    {
        var settingValue = settingManager.GetSettingValueForTenant(AppSettings.ExternalLoginProvider.Tenant.WsFederation, this.abpSession.TenantId.Value);
        var settings = settingValue.FromJsonString<WsFederationExternalLoginProviderSettings>();
        return this.CreateExternalLoginInfo(settings);
    }

    protected override ExternalLoginProviderInfo GetHostInformation()
    {
        var settingValue = settingManager.GetSettingValueForApplication(AppSettings.ExternalLoginProvider.Host.WsFederation);
        var settings = settingValue.FromJsonString<WsFederationExternalLoginProviderSettings>();
        return this.CreateExternalLoginInfo(settings);
    }
}