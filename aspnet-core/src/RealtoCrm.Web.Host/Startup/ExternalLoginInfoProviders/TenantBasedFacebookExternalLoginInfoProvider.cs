using Abp.AspNetZeroCore.Web.Authentication.External;
using Abp.AspNetZeroCore.Web.Authentication.External.Facebook;
using Abp.Configuration;
using Abp.Dependency;
using Abp.Extensions;
using Abp.Json;
using Abp.Runtime.Caching;
using Abp.Runtime.Session;
using RealtoCrm.Authentication;
using RealtoCrm.Configuration;

namespace RealtoCrm.Web.Startup.ExternalLoginInfoProviders;

public class TenantBasedFacebookExternalLoginInfoProvider(
    ISettingManager settingManager,
    IAbpSession abpSession,
    ICacheManager cacheManager) : TenantBasedExternalLoginInfoProviderBase(abpSession, cacheManager), ISingletonDependency
{
    private readonly IAbpSession abpSession = abpSession;
    public override string Name { get; } = FacebookAuthProviderApi.Name;

    private ExternalLoginProviderInfo CreateExternalLoginInfo(FacebookExternalLoginProviderSettings settings)
    {
        return new ExternalLoginProviderInfo(this.Name, settings.AppId, settings.AppSecret, typeof(FacebookAuthProviderApi));
    }
        
    protected override bool TenantHasSettings()
    {
        var settingValue = settingManager.GetSettingValueForTenant(AppSettings.ExternalLoginProvider.Tenant.Facebook, this.abpSession.TenantId.Value);
        return !settingValue.IsNullOrWhiteSpace();
    }

    protected override ExternalLoginProviderInfo GetTenantInformation()
    {
        var settingValue = settingManager.GetSettingValueForTenant(AppSettings.ExternalLoginProvider.Tenant.Facebook, this.abpSession.TenantId.Value);
        var settings = settingValue.FromJsonString<FacebookExternalLoginProviderSettings>();
        return this.CreateExternalLoginInfo(settings);
    }

    protected override ExternalLoginProviderInfo GetHostInformation()
    {
        var settingValue = settingManager.GetSettingValueForApplication(AppSettings.ExternalLoginProvider.Host.Facebook);
        var settings = settingValue.FromJsonString<FacebookExternalLoginProviderSettings>();
        return this.CreateExternalLoginInfo(settings);
    }
}