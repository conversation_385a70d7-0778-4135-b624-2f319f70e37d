using System;
using System.IO;
using System.Linq;
using System.Reflection;
using Abp.AspNetCore;
using Abp.AspNetCore.Mvc.Antiforgery;
using Abp.Castle.Logging.Log4Net;
using Abp.Extensions;
using Abp.HtmlSanitizer;
using Abp.PlugIns;
using Castle.Facilities.Logging;
using Hangfire;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Owl.reCAPTCHA;
using RealtoCrm.Identity;
using RealtoCrm.Web.Common;
using RealtoCrm.Web.HealthCheck;
using RealtoCrm.Web.OpenIddict;
using RealtoCrm.Web.Startup;
using RealtoCrm.Web.Swagger;
using static RealtoCrm.Web.WebHostConstants.Cors;
using IPAddress = System.Net.IPAddress;
using HealthChecksUISettings = HealthChecks.UI.Configuration.Settings;

namespace RealtoCrm.Web.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceProvider AddRealtoCrmServices(
        this IServiceCollection services,
        IConfigurationRoot appConfiguration,
        IWebHostEnvironment hostingEnvironment)
        => services
            .AddRealtoWeb()
            .AddRealtoSignalR()
            .AddRealtoCors(appConfiguration)
            .AddRealtoKestrel(appConfiguration)
            .AddRealtoAuth(appConfiguration)
            .AddRealtoAuthConfiguration(appConfiguration)
            .AddRealtoSwagger(appConfiguration)
            .AddRealtoCaptcha(appConfiguration)
            .AddRealtoHangfire(appConfiguration)
            .AddRealtoHealthChecks(appConfiguration)
            .AddRealtoAbp(hostingEnvironment);

    private static IServiceCollection AddRealtoWeb(this IServiceCollection services)
    {
        services.AddControllersWithViews(options =>
            {
                options.Filters.Add(new AbpAutoValidateAntiforgeryTokenAttribute());
                options.AddAbpHtmlSanitizer();
            })
#if DEBUG
            .AddRazorRuntimeCompilation()
#endif
            .AddNewtonsoftJson();

        return services;
    }

    private static IServiceProvider AddRealtoAbp(this IServiceCollection services, IWebHostEnvironment hostingEnvironment)
        => services.AddAbp<RealtoCrmWebHostModule>(options =>
        {
            //Configure Log4Net logging
            options.IocManager.IocContainer.AddFacility<LoggingFacility>(
                f => f.UseAbpLog4Net().WithConfig(hostingEnvironment.IsDevelopment()
                    ? "log4net.config"
                    : "log4net.Production.config")
            );

            options.PlugInSources.AddFolder(Path.Combine(hostingEnvironment.WebRootPath, "Plugins"),
                SearchOption.AllDirectories);
        });

    private static IServiceCollection AddRealtoHealthChecks(this IServiceCollection services, IConfigurationRoot appConfiguration)
    {
        if (bool.Parse(appConfiguration["HealthChecks:HealthChecksEnabled"]))
        {
            services.AddHealthChecks(appConfiguration);
        }

        return services;
    }


    private static IServiceCollection AddRealtoHangfire(this IServiceCollection services, IConfiguration appConfiguration)
    {
        if (WebConsts.HangfireDashboardEnabled)
        {
            //Hangfire(Enable to use Hangfire instead of default job manager)
            services.AddHangfire(config => { config.UseSqlServerStorage(appConfiguration.GetConnectionString("Default")); });

            services.AddHangfireServer();
        }

        return services;
    }

    private static IServiceCollection AddRealtoCaptcha(this IServiceCollection services, IConfiguration appConfiguration)
        => services.AddreCAPTCHAV3(x =>
        {
            x.SiteKey = appConfiguration["Recaptcha:SiteKey"];
            x.SiteSecret = appConfiguration["Recaptcha:SecretKey"];
        });

    private static IServiceCollection AddRealtoSwagger(this IServiceCollection services, IConfiguration appConfiguration)
    {
        if (!WebConsts.SwaggerUiEnabled)
        {
            return services;
        }

        //Swagger - Enable this line and the related lines in Configure method to enable swagger UI
        services
            .AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo() { Title = "RealtoCrm API", Version = "v1" });
                options.DocInclusionPredicate((docName, description) => true);
                options.ParameterFilter<SwaggerEnumParameterFilter>();
                options.SchemaFilter<SwaggerEnumSchemaFilter>();
                options.OperationFilter<SwaggerOperationIdFilter>();
                options.OperationFilter<SwaggerOperationFilter>();
                options.OperationFilter<SwaggerFixUrlFilter>();
                options.CustomDefaultSchemaIdSelector();

                options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    In = ParameterLocation.Header,
                    Description = "Please enter token",
                    Name = "Authorization",
                    Type = SecuritySchemeType.Http,
                    BearerFormat = "JWT",
                    Scheme = "bearer"
                });

                options.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer",
                            }
                        },
                        Array.Empty<string>()
                    }
                });

                //add summaries to swagger
                if (!appConfiguration.GetValue<bool>("Swagger:ShowSummaries"))
                {
                    return;
                }

                var hostXmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var hostXmlPath = Path.Combine(AppContext.BaseDirectory, hostXmlFile);
                options.IncludeXmlComments(hostXmlPath);

                const string applicationXml = $"RealtoCrm.Application.xml";
                var applicationXmlPath = Path.Combine(AppContext.BaseDirectory, applicationXml);
                options.IncludeXmlComments(applicationXmlPath);

                const string webCoreXmlFile = $"RealtoCrm.Web.Core.xml";
                var webCoreXmlPath = Path.Combine(AppContext.BaseDirectory, webCoreXmlFile);
                options.IncludeXmlComments(webCoreXmlPath);
            });

        return services;
    }

    private static IServiceCollection AddRealtoAuthConfiguration(this IServiceCollection services, IConfigurationRoot appConfiguration)
    {
        if (bool.Parse(appConfiguration["OpenIddict:IsEnabled"]))
        {
            services.AddOpenIddictConfiguration(appConfiguration);

            services.Configure<CookieAuthenticationOptions>(IdentityConstants.ApplicationScheme,
                options => { options.LoginPath = "/Ui/Login"; });
        }
        else
        {
            services.Configure<SecurityStampValidatorOptions>(opts => { opts.OnRefreshingPrincipal = SecurityStampValidatorCallback.UpdatePrincipal; });
        }

        return services;
    }

    private static IServiceCollection AddRealtoSignalR(this IServiceCollection services)
    {
        services.AddSignalR();
        return services;
    }

    private static IServiceCollection AddRealtoCors(this IServiceCollection services, IConfigurationRoot appConfiguration)
        => services.AddCors(options =>
        {
            options.AddPolicy(DefaultCorsPolicyName, builder =>
            {
                //App:CorsOrigins in appsettings.json can contain more than one address split by comma.
                builder
                    .WithOrigins(
                        // App:CorsOrigins in appsettings.json can contain more than one address separated by comma.
                        appConfiguration["App:CorsOrigins"]
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.RemovePostFix("/"))
                            .ToArray()
                    )
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });


    private static IServiceCollection AddRealtoKestrel(this IServiceCollection services, IConfigurationRoot appConfiguration)
    {
        if (bool.Parse(appConfiguration["KestrelServer:IsEnabled"]))
        {
            services
                .Configure<Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions>(options => { options.Listen(IPAddress.Any, 80); });
        }

        return services;
    }

    private static IServiceCollection AddRealtoAuth(this IServiceCollection services, IConfigurationRoot appConfiguration)
    {
        services.AddIdentity();
        services.AddAuth(appConfiguration);
        return services;
    }

    private static IServiceCollection AddHealthChecks(this IServiceCollection services, IConfigurationRoot appConfiguration)
    {
        services.AddAbpZeroHealthCheck();

        var healthCheckUiSection = appConfiguration.GetSection("HealthChecks")?.GetSection("HealthChecksUI");

        if (bool.Parse(healthCheckUiSection["HealthChecksUIEnabled"]))
        {
            services.Configure<HealthChecksUISettings>(settings => { healthCheckUiSection.Bind(settings, c => c.BindNonPublicProperties = true); });
            services.AddHealthChecksUI()
                .AddInMemoryStorage();
        }

        return services;
    }
}