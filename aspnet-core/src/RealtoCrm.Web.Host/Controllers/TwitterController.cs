using System.Linq;
using System.Threading.Tasks;
using Abp.AspNetZeroCore.Web.Authentication.External;
using Abp.AspNetZeroCore.Web.Authentication.External.Twitter;
using Abp.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RealtoCrm.Configuration;

namespace RealtoCrm.Web.Controllers;

[Route("api/[controller]/[action]")]
public class TwitterController(
    ExternalAuthConfiguration externalAuthConfiguration,
    IAppConfigurationAccessor appConfigurationAccessor) : RealtoCrmControllerBase
{
    private readonly IConfigurationRoot appConfiguration = appConfigurationAccessor.Configuration;

    [HttpPost]
    public async Task<TwitterGetRequestTokenResponse> GetRequestToken()
    {
        var loginInfoProvider = externalAuthConfiguration.ExternalLoginInfoProviders.Find(
            e => e.Name == TwitterAuthProviderApi.Name
        );

        if (loginInfoProvider == null)
        {
            throw new UserFriendlyException("Twitter login configuration is missing !");
        }

        var loginInfo = loginInfoProvider.GetExternalLoginInfo();
        var callbackUrl = this.appConfiguration["App:ClientRootAddress"].EnsureEndsWith('/') + "account/login";
            
        var twitter = new TwitterAuthProviderApi();
        return await twitter.GetRequestToken(
            loginInfo.ClientId,
            loginInfo.ClientSecret,
            callbackUrl);
    }

    [HttpPost]
    public async Task<TwitterGetAccessTokenResponse> GetAccessToken(string token, string verifier)
    {
        var twitter = new TwitterAuthProviderApi();
        return await twitter.GetAccessToken(token, verifier);
    }
}