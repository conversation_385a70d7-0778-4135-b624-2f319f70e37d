#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY ["src/RealtoCrm.DataImporter/RealtoCrm.DataImporter.csproj", "src/RealtoCrm.DataImporter/"]
COPY ["src/RealtoCrm.EntityFrameworkCore/RealtoCrm.EntityFrameworkCore.csproj", "src/RealtoCrm.EntityFrameworkCore/"]
COPY ["src/RealtoCrm.Core/RealtoCrm.Core.csproj", "src/RealtoCrm.Core/"]
COPY ["src/RealtoCrm.Core.Shared/RealtoCrm.Core.Shared.csproj", "src/RealtoCrm.Core.Shared/"]
RUN dotnet restore "src/RealtoCrm.DataImporter/RealtoCrm.DataImporter.csproj"
COPY . .
WORKDIR "/src/src/RealtoCrm.DataImporter"
RUN dotnet build "RealtoCrm.DataImporter.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "RealtoCrm.DataImporter.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "RealtoCrm.DataImporter.dll", "-s"]
