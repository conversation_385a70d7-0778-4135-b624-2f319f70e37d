namespace RealtoCrm.DataImporter.Services.Models;

using AutoMapper;
using Newtonsoft.Json;
using Companies;
using Mapping;
using static Constants.ImporterConstants;

public class DivisionsAsiImportModel : IMapFrom<Division>, IMapExplicitly
{
    public int Id { get; set; }

    public string? Name { get; set; }

    [JsonProperty("parent_id")]
    public int ParentId { get; set; }

    public int? Franchise { get; set; }

    public int? Visible { get; set; }

    public bool Brand { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<DivisionsAsiImportModel, Division>()
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name ?? string.Empty))
            .ForMember(dest => dest.IsActive, opt => opt
                .MapFrom(src => src.Visible != 0))
            .ForMember(dest => dest.CompanyId, cfg => cfg
                .MapFrom(dest => dest.Brand ? ImotekaCompanyId : AddressCompanyId))
            .ForMember(dest => dest.TenantId, cfg => cfg
                .MapFrom(dest => dest.Brand ? ImotekaTenantId : AddressTenantId))
            .ForMember(dest => dest.Id, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Offices, cfg => cfg
                .Ignore());
}