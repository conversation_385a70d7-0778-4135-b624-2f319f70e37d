namespace RealtoCrm.DataImporter.Services.Models;

using System.Collections.Generic;
using AutoMapper;
using Employees.Models;
using Mapping;
using MultiTenancy.Models;

public class EmployeeFortonImportModel : IMapTo<UserWithEmployeeCreateRequestModel>, IMapExplicitly
{
    private const string DefaultPassword = "#LG&jFYGoifZhxfXH52a*S";

    public string Username { get; init; } = default!;

    public string Email { get; init; } = default!;

    public string FirstName { get; init; } = default!;

    public string MiddleName { get; init; } = default!;

    public string LastName { get; init; } = default!;

    public string DisplayName { get; init; } = default!;

    public string? PhoneNumber { get; init; }

    public string? SimCardNumber { get; init; }

    public string? WorkPosition { get; init; }

    public string? IdentificationNumber { get; init; }

    public int? OfficeId { get; set; }

    public int? TeamId { get; init; }

    public int? DepartmentId { get; init; }

    public int? DivisionId { get; init; }

    public int? CompanyId { get; init; }

    public int? CompanyPositionId { get; init; }

    public int? ManagerId { get; set; }

    public int? AssistsToId { get; init; }

    public bool IsActive { get; init; } = true;

    public IEnumerable<int> RoleIds { get; init; } = [];

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<EmployeeFortonImportModel, UserWithEmployeeCreateRequestModel>()
            .ForMember(m => m.User, cfg => cfg
                .MapFrom(m => new UserCreateRequestModel
                {
                    UserName = m.Username,
                    EmailAddress = m.Email,
                    Password = DefaultPassword,
                    ShouldChangePasswordOnNextLogin = true,
                    IsActive = m.IsActive,
                    RoleIds = m.RoleIds,
                }))
            .ForMember(m => m.Employee, cfg => cfg
                .MapFrom(m => new EmployeeRequestModel
                {
                    FirstName = m.FirstName,
                    MiddleName = m.MiddleName,
                    LastName = m.LastName,
                    DisplayName = m.DisplayName,
                    PhoneNumber = m.PhoneNumber,
                    SimCardNumber = m.SimCardNumber,
                    WorkPosition = m.WorkPosition,
                    IdentificationNumber = m.IdentificationNumber,
                    OfficeId = m.OfficeId,
                    TeamId = m.TeamId,
                    DepartmentId = m.DepartmentId,
                    DivisionId = m.DivisionId,
                    CompanyId = m.CompanyId,
                    CompanyPositionId = m.CompanyPositionId,
                    ManagerId = m.ManagerId,
                    AssistsToId = m.AssistsToId,
                    IsDeleted = !m.IsActive,
                }));
}