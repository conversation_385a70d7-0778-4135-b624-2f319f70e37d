namespace RealtoCrm.DataImporter.Services.Models;

using System;
using System.Globalization;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.DataImporter.Services.Mappings;
using Mapping;
using Money;
using Searches;

public class SearchAsiImportModel : IMapTo<Search>, IMapExplicitly
{
    private const int SearchTypeBuyAsiId = 1;

    private const int EuroAsiValue = 1;

    private const int BgnAsiValue = 2;

    private const int FacingDirectionSouthId = 5;

    private const int GarageNomenclatureId = 1;

    private const int DefaultSourceCategoryId = 1;

    [JsonProperty("id")]
    public int? SearchAsiId { get; set; }

    [JsonProperty("client_id")]
    public int? ClientId { get; set; }

    [JsonProperty("user_id")]
    public int? UserId { get; set; }

    [JsonProperty("type_id")]
    public int? TypeId { get; set; }

    [JsonProperty("agency_id")]
    public int? AgencyId { get; set; } = default!;

    [JsonProperty("archive_reason_id")]
    public int? ArchiveReasonId { get; set; }

    [JsonProperty("archive_date")]
    public string? ArchiveDate { get; set; } = default!;

    [JsonProperty("currency_id")]
    public int? CurrencyId { get; set; }

    [JsonProperty("price_from")]
    public double? PriceFrom { get; set; }

    [JsonProperty("price_to")]
    public double? PriceTo { get; set; }

    [JsonProperty("square_from")]
    public double? SquareFrom { get; set; }

    [JsonProperty("square_to")]
    public double? SquareTo { get; set; }

    [JsonProperty("source_id")]
    public int? SourceId { get; set; }

    [JsonProperty("source_detail_id")]
    public int? SourceDetailId { get; set; }

    [JsonProperty("bedrooms_from")]
    public int? BedroomsFrom { get; set; }

    [JsonProperty("bedrooms_to")]
    public int? BedroomsTo { get; set; }

    [JsonProperty("bathrooms_from")]
    public int? BathroomsFrom { get; set; }

    [JsonProperty("bathrooms_to")]
    public int? BathroomsTo { get; set; }

    [JsonProperty("terraces_from")]
    public int? TerracesFrom { get; set; }

    [JsonProperty("terraces_to")]
    public int? TerracesTo { get; set; }

    [JsonProperty("rooms_from")]
    public int? RoomsFrom { get; set; }

    [JsonProperty("rooms_to")]
    public int? RoomsTo { get; set; }

    [JsonProperty("building_year_from")]
    public int? BuildingYearFrom { get; set; }

    [JsonProperty("building_year_to")]
    public int? BuildingYearTo { get; set; }

    [JsonProperty("floor_from")]
    public int? FloorFrom { get; set; }

    [JsonProperty("floor_to")]
    public int? FloorTo { get; set; }

    [JsonProperty("elevator")]
    public int? Elevator { get; set; }

    [JsonProperty("garage")]
    public int? Garage { get; set; }

    [JsonProperty("attic")]
    public int? Attic { get; set; }

    [JsonProperty("south_exposure")]
    public int? SouthExposure { get; set; }

    [JsonProperty("deal_motive_id")]
    public int? DealMotiveId { get; set; }

    [JsonProperty("financing_type_id")]
    public int? FinancingTypeId { get; set; }

    [JsonProperty("contract_type")]
    public string? ContractType { get; set; }

    [JsonProperty("credit_center")]
    public int? CreditCenter { get; set; }

    [JsonProperty("created_at")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    [JsonProperty("created_by")]
    public int? CreatedBy { get; set; }

    [JsonProperty("updated_by")]
    public int? UpdatedBy { get; set; }

    [JsonProperty("countries")]
    public IEnumerable<AsiObject>? Countries { get; set; }

    [JsonProperty("estate_types")]
    public IEnumerable<AsiObject>? EstateTypes { get; set; }

    [JsonProperty("regions")]
    public IEnumerable<AsiObject>? Regions { get; set; }

    [JsonProperty("municipalities")]
    public IEnumerable<AsiObject>? Municipalities { get; set; }

    [JsonProperty("locations")]
    public IEnumerable<AsiObject>? Locations { get; set; }

    [JsonProperty("quarters")]
    public IEnumerable<AsiObject>? Quarters { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<SearchAsiImportModel, Search>()
            .ForMember(dest => dest.Type, opt =>
                opt.MapFrom(src => src.TypeId == SearchTypeBuyAsiId ? SearchType.Buy : SearchType.Rent))
            .ForMember(dest => dest.SearchDetail, opt =>
                opt.MapFrom(src => new SearchDetail()))
            .ForMember(dest => dest.MoneyFrom, opt =>
                opt.MapFrom(src => new Money
                {
                    Amount = (decimal)src.PriceFrom.GetValueOrDefault(),
                    Currency = Currency.EUR
                }))
            .ForMember(dest => dest.MoneyTo, opt =>
                opt.MapFrom(src => new Money
                {
                    Amount = (decimal)src.PriceTo.GetValueOrDefault(),
                    Currency = Currency.EUR
                }))
            .ForMember(dest => dest.AreaFrom, opt =>
                opt.MapFrom(src => src.SquareFrom ?? 0))
            .ForMember(dest => dest.AreaTo, opt =>
                opt.MapFrom(src => src.SquareTo ?? 0))
            .ForMember(dest => dest.ArchiveDate, opt =>
                opt.MapFrom(src =>
                    !string.IsNullOrEmpty(src.ArchiveDate)
                        ? DateTime.ParseExact(src.ArchiveDate, "yyyy-MM-dd", CultureInfo.InvariantCulture)
                        : (DateTime?)null))
            .ForMember(dest => dest.ArchiveReasonId, opt =>
            {
                opt.PreCondition(src => (src.ArchiveReasonId > 0 && src.ArchiveReasonId != null) && 
                    ArchiveReasonsAsiImporterMapping.AsiArchiveReasonIdToArchiveReasonId.ContainsKey(
                        (int)src.ArchiveReasonId));
                opt.MapFrom(src =>
                    ArchiveReasonsAsiImporterMapping.AsiArchiveReasonIdToArchiveReasonId[(int)src.ArchiveReasonId!]);
            })
            .ForMember(c => c.SearchMapping, opt => opt.MapFrom(cm => new SearchMapping
            {
                AsiId = cm.SearchAsiId
            }))
            .ForMember(dest => dest.CreationTime, opt =>
                opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.LastModificationTime, opt =>
                opt.MapFrom(src => src.UpdatedAt))
            .ForMember(dest => dest.SearchesContractTypes, opt =>
            {
                opt.PreCondition(src => src.ContractType != null &&
                                        ContractTypeAsiImporterMappings.AsiContractTypeIdToContractTypeId.ContainsKey(
                                            src.ContractType));
                opt.MapFrom(src => new List<SearchContractType>
                {
                    new SearchContractType
                    {
                        ContractTypeId =
                            ContractTypeAsiImporterMappings.AsiContractTypeIdToContractTypeId[src.ContractType!]
                    }
                });
            })
            .ForMember(dest => dest.WithCreditCenter,
                opt => { opt.MapFrom(src => src.CreditCenter != null && src.CreditCenter == 1 ? true : false); })
            .ForMember(dest => dest.SearchesDealMotives, opt =>
            {
                opt.PreCondition(src => src.DealMotiveId != null &&
                                        DealMotivesAsiImporterMappings.AsiDealMotiveIdToDealMotiveId.ContainsKey(
                                            (int)src.DealMotiveId));
                opt.MapFrom(src => new List<SearchDealMotive>
                {
                    new SearchDealMotive
                    {
                        DealMotiveId =
                            DealMotivesAsiImporterMappings.AsiDealMotiveIdToDealMotiveId[(int)src.DealMotiveId!]
                    }
                });
            })
            .ForMember(dest => dest.SearchesFinancing, opt =>
            {
                opt.PreCondition(src => src.FinancingTypeId != null &&
                                        FinancingAsiImporterMappings.AsiFinancingTypeIdToFinancingId.ContainsKey(
                                            (int)src.FinancingTypeId));
                opt.MapFrom(src => new List<SearchFinancing>
                {
                    new SearchFinancing
                    {
                        FinancingId =
                            FinancingAsiImporterMappings.AsiFinancingTypeIdToFinancingId[(int)src.FinancingTypeId!]
                    }
                });
            })
            .ForMember(dest => dest.SearchesFacingDirections, opt =>
            {
                opt.PreCondition(src => src.SouthExposure == 1);
                opt.MapFrom(src => new List<SearchFacingDirection>
                {
                    new SearchFacingDirection
                    {
                        FacingDirectionId = FacingDirectionSouthId
                    }
                });
            })
            .ForMember(dest => dest.SearchesGarages, opt =>
            {
                opt.PreCondition(src => src.Garage == 1);
                opt.MapFrom(src => new List<SearchGarage>
                {
                    new SearchGarage
                    {
                        GarageId = GarageNomenclatureId
                    }
                });
            })
            .ForMember(dest => dest.SourceCategoryId, opt =>
            {
                opt.MapFrom(src =>
                    src.SourceDetailId != null &&
                    SourceCategoriesAsiImporterMappings.AsiSourceIdToSourceCategoryId.ContainsKey(
                        (int)src.SourceDetailId)
                        ? SourceCategoriesAsiImporterMappings.AsiSourceIdToSourceCategoryId[(int)src.SourceDetailId]
                        : DefaultSourceCategoryId);
            })
            .ForMember(dest => dest.TerracesCount, opt =>
                opt.MapFrom(src => CalculateTerracesCount(src.TerracesFrom, src.TerracesTo))
            )
            .ForMember(dest => dest.SearchesCountries, opt =>
            {
                opt.PreCondition(src => src.Countries != null && src.Countries.Any());
                opt.MapFrom(src => src.Countries!
                    .Where(country => CountryAsiImpoterMappings.AsiCountryIdToCountryId.ContainsKey(country.Id))
                    .Select(country => new SearchCountry
                    {
                        CountryId = CountryAsiImpoterMappings.AsiCountryIdToCountryId[country.Id]
                    })
                    .ToList());
            })
            .ForMember(dest => dest.SearchesEstateTypes, opt =>
            {
                opt.PreCondition(src => src.EstateTypes != null && src.EstateTypes.Any());
                opt.MapFrom(src => src.EstateTypes!
                    .Where(estateType =>
                        OfferAsiImporterMappings.AsiEstateTypeIdToEstateTypeId.ContainsKey(estateType.Id))
                    .Select(estateType => new SearchEstateType
                    {
                        EstateTypeId = OfferAsiImporterMappings.AsiEstateTypeIdToEstateTypeId[estateType.Id]
                    })
                    .ToList());
            });

    private static int CalculateTerracesCount(int? terracesFrom, int? terracesTo)
    {
        if (terracesFrom.HasValue && terracesTo.HasValue)
        {
            return (int)Math.Ceiling((terracesFrom.Value + terracesTo.Value) / 2.0);
        }

        return terracesFrom ?? terracesTo ?? 0;
    }

    public class AsiObject
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("pivot")]
        public object Pivot { get; set; } = default!;
    }
}