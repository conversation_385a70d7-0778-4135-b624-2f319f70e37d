using System;
using AutoMapper;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Addresses;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models;

public class ProvinceUesImportModel : IMapTo<Province>, IMapExplicitly
{
    [JsonProperty("id")]
    public int AdminId { get; set; }

    [JsonProperty("country_id")]
    public int CountryId { get; set; }
    
    [JsonProperty("is_visible")]
    public bool? IsVisible { get; set; }

    public string Name { get; set; } = default!;
    
    [JsonProperty("created_at")]
    public string? CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public string? UpdatedAt { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<ProvinceUesImportModel, Province>()
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name ?? string.Empty))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => src.IsVisible ?? true))
            .ForMember(
                dest => dest.Country,
                opt => opt.MapFrom<CountryValueResolver>())
            .ForMember(dest => dest.CountryId, cfg => cfg.Ignore())
            .ForMember(dest => dest.CreationTime, cfg => cfg
                .MapFrom(src =>
                    string.IsNullOrEmpty(src.CreatedAt) ? DateTime.MinValue : DateTime.Parse(src.CreatedAt)))
            .ForMember(dest => dest.LastModificationTime, cfg => cfg
                .MapFrom(src => string.IsNullOrEmpty(src.UpdatedAt) ? (DateTime?)null : DateTime.Parse(src.UpdatedAt)))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => true));
}