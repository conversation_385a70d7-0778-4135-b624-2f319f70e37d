using System;
using System.Collections.Generic;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models;

public class MunicipalitiesUesImportModel : IMapTo<Municipality>, IMapExplicitly
{
    public int Id { get; set; }

    [JsonProperty("region_id")] public int? RegionId { get; set; }

    public string? Name { get; set; }

    public string? Code { get; set; } = default!;
    
    [JsonProperty("is_visible")]
    public bool IsVisible { get; set; }

    public string? Ekatte { get; set; } = default!;

    public string? CreatedAt { get; set; }

    public string? UpdatedAt { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<MunicipalitiesUesImportModel, Municipality>()
            .ForMember(dest => dest.Id, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name))
            .ForMember(dest => dest.Ekatte, cfg => cfg
                .MapFrom(src => src.Ekatte ?? string.Empty))
            .ForMember(dest => dest.Code, cfg => cfg
                .MapFrom(src => src.Code ?? string.Empty))
            .ForMember(dest => dest.CreationTime, cfg => cfg
                .MapFrom(src =>
                    string.IsNullOrEmpty(src.CreatedAt) ? DateTime.MinValue : DateTime.Parse(src.CreatedAt)))
            .ForMember(dest => dest.LastModificationTime, cfg => cfg
                .MapFrom(src => string.IsNullOrEmpty(src.UpdatedAt) ? (DateTime?)null : DateTime.Parse(src.UpdatedAt)))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => true));
}