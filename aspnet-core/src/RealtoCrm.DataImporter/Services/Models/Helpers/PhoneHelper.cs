using System;
using System.Linq;

namespace RealtoCrm.DataImporter.Services.Models.Helpers;

public static class PhoneHelper
{
    public static string NormalizePhoneNumber(string phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
            return phone;

        phone = phone.Trim();

        const string bulgarianNumbersPrefix = "+359";

        if (phone.StartsWith($"0"))
        {
            return bulgarianNumbersPrefix + phone[1..];
        }

        if (phone.StartsWith("00359"))
        {
            return string.Concat(bulgarianNumbersPrefix, phone.AsSpan(5));
        }

        if (phone.StartsWith("359"))
        {
            return bulgarianNumbersPrefix + phone[3..];
        }

        return phone;
    }
}