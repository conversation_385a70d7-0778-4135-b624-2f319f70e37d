namespace RealtoCrm.DataImporter.Services.Implementation
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Models.ResponseModels;
    using System.Threading.Tasks;
    using Abp.Domain.Uow;
    using Abp.EntityFrameworkCore;
    using AutoMapper;
    using Microsoft.Extensions.Configuration;
    using Models;
    using Companies;

    public class DivisionsAsiImporterService : BaseAsiImporterService<Division, DivisionsAsiImportModel>
    {
        private readonly DivisionsSecondAsiImporterService secondaryImporterService;
        private readonly FranchiseDivisionsAsiImporterService franchiseImporterService;

        public DivisionsAsiImporterService(
            Log log,
            IMapper mapper,
            IConfiguration configuration,
            IDbContextResolver dbContextResolver,
            IConnectionStringResolver connectionStringResolver,
            IJsonSerializerService<DivisionsAsiImportModel> jsonSerializerService,
            IImportDocumentFetchService importDocumentFetchService,
            DivisionsSecondAsiImporterService secondaryImporterService,
            FranchiseDivisionsAsiImporterService franchiseImporterService,
            IAddressMappingService addressMappingService,
            IMapMoneyService mapMoneyService,
            IMapSourceCategoryService mapSourceCategoryService)
            : base(
                log,
                mapper,
                configuration,
                dbContextResolver,
                connectionStringResolver,
                jsonSerializerService,
                importDocumentFetchService,
                addressMappingService,
                mapMoneyService,
                mapSourceCategoryService)
        {
            this.secondaryImporterService = secondaryImporterService;
            this.franchiseImporterService = franchiseImporterService;
        }

        protected override string ImportDocumentUrl => this.DefaultUrl + "/office?office_structure_id=5";

        /// <summary>
        /// Override of import method to perform two separate imports from two different URLs
        /// Reason: We fetch divisions from two different URLs
        /// </summary>
        public override async Task<int> Import()
        {
            int totalImportedCount = 0;

            Console.WriteLine($"First import");
            totalImportedCount += await base.Import();

            Console.WriteLine($"Second import");
            totalImportedCount += await secondaryImporterService.Import();

            Console.WriteLine("Franchise import");
            totalImportedCount += await franchiseImporterService.Import();

            Console.WriteLine($"Total Divisions fetched: {totalImportedCount}");
            return totalImportedCount;
        }

        protected override async Task DecorateAfter(IEnumerable<Division> entityList,
            List<DivisionsAsiImportModel> batchAsiData)
        {
            var namesFromAsi = batchAsiData
                .Select(p => p.Name)
                .ToHashSet();

            var currentDivisions = this.Mapper
                .ProjectTo<ImporterResponseModel>(this
                    .DbContext
                    .Divisions
                    .Where(d => namesFromAsi.Contains(d.Name)))
                .ToList();

            var divisionsMapping = entityList
                .Select(division =>
                {
                    var correspondingDivision = batchAsiData.FirstOrDefault(d => d.Name == division.Name);
                    if (correspondingDivision == null)
                    {
                        return null;
                    }

                    var currentDivision = currentDivisions.FirstOrDefault(o => o.Name == division.Name);
                    if (currentDivision == null)
                    {
                        return null;
                    }

                    return new DivisionMapping
                    {
                        DivisionId = currentDivision.Id,
                        AsiId = correspondingDivision.Id
                    };
                })
                .Where(mapping => mapping != null)
                .ToList();

            if (divisionsMapping.Any())
            {
                await this.DbContext.DivisionMappings.AddRangeAsync(divisionsMapping!);
                await this.DbContext.SaveChangesAsync();
            }
        }
    }
}