namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Companies;
using Mappings.DataResolvers.Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.Mappings;

public class TeamsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<TeamsUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Team, TeamsUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/teams";

    protected override int GetTotalPageCount(string jsonString) => 1;

    protected override async Task<IEnumerable<TeamsUesImportModel>> GetEntitiesForPages(int startPage, int batchSize)
    {
        log.Write($"Fetching UES Teams");

        string jsonContent = await this.ImportDocumentFetchService
            .FetchDocumentAsync(this.ImportDocumentUrl, this.ApiKey);

        var data = await jsonSerializerService.DeserializePageAsync(jsonContent);

        var fetchedDataCount = data?.Count() ?? 0;
        log.Write($"Fetched {fetchedDataCount} teams");

        return data ?? [];
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var company = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == UniqueEstatesCompanyName)
            .SingleAsync();

        var departmentMappings = await this.Mapper
            .ProjectTo<DepartmentManagerUsersUesMappingModel>(this.DbContext.DepartmentManagerUsersUesMapping)
            .ToListAsync();

        return new Dictionary<string, object>
        {
            { TenantValueResolver<TeamsUesImportModel, Team>.CompanyNameItemsKey, company },
            { CompanyValueResolver<TeamsUesImportModel, Team>.CompanyItemsKey, company },
            { DepartmentValueResolver.DepartmentsItemsKey, departmentMappings }
        };
    }

    protected override async Task DecorateAfter(IEnumerable<Team> entityList,
        List<TeamsUesImportModel> batchAsiData)
    {
        var teamsMappings = entityList
            .Select(team =>
            {
                var correspondingTeam = batchAsiData
                    .FirstOrDefault(o => o.TeamName == team.Name);

                if (correspondingTeam == null)
                {
                    return null;
                }

                var currentTeam = entityList.FirstOrDefault(o => o.Name == team.Name);

                if (currentTeam == null)
                {
                    return null;
                }

                return new TeamTeamLeaderUsersUesMapping()
                {
                    TeamId = currentTeam.Id,
                    TeamLeaderId = correspondingTeam.TeamLeaderId,
                    UserIds = correspondingTeam.Users.ToList(),
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (teamsMappings.Any())
        {
            await this.DbContext.TeamTeamLeaderUsersUesMapping.AddRangeAsync(teamsMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }
}