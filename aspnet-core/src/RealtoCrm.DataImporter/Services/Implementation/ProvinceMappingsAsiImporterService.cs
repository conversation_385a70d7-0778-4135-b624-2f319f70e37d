namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.DataImporter.Services.Models.ResponseModels;
using RealtoCrm.Extensions;
using RealtoCrm.Nomenclatures;

public class ProvinceMappingsAsiImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<ProvinceAsiImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseAsiImporterService<Province, ProvinceAsiImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/region";
    //key: province name in ASI, value: province name from Seeder/in DB
    private readonly Dictionary<string, string> exceptionMappings = new()
    {
        { "София", "София (област)" },
    };

    public override async Task<int> Import()
    {
        log.Write("Started ProvinceMapping import...");

        var count = 0;
        var pageNumber = 1;

        Console.WriteLine("Fetching initial page");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);
        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        var provincesList = this.Mapper
            .ProjectTo<AdministrativeDivisionImporterResponseModel>(this
                .DbContext
                .Provinces)
            .ToList();

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < BatchSize ? remainingPages : BatchSize;
            var startTime = DateTime.Now;

            var batchData = await this.GetEntitiesForPages(pageNumber, currentBatchSize).ToListAsync();

            if (!batchData.Any())
            {
                break;
            }

            IEnumerable<ProvinceMapping> provinceMappings = batchData.Select(province =>
                {
                    if (exceptionMappings.TryGetValue(province.Name, out var mappedName))
                    {
                        province.Name = mappedName;
                    }

                    var correspondingProvince = provincesList.FirstOrDefault(p => p.Name == province.Name);
                    if (correspondingProvince != null)
                    {
                        return new ProvinceMapping { ProvinceId = correspondingProvince.Id, AsiId = province.Id };
                    }

                    return null;
                })
                .OfType<ProvinceMapping>()
                .ToList();

            if (provinceMappings.Any())
            {
                await this.DbContext.ProvinceMappings.AddRangeAsync(provinceMappings);
                await this.DbContext.SaveChangesAsync();
            }

            var duration = (DateTime.Now - startTime).TotalSeconds;

            Console.WriteLine($"{batchData.Count} Provinces inserted in {duration} seconds");

            count += batchData.Count;
            pageNumber += currentBatchSize;
        }

        log.Write("Fetched count: " + count);

        log.Write("Finished import.");

        return count;
    }
}