namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Authorization.Roles;
using AutoMapper;
using Employees;
using Employees.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;
using static CosherConsts.Tenants;

public class EmployeesNewEstatesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IEmployeesAppService employeesAppService,
    IJsonSerializerService<EmployeesNewEstatesImporterService> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Employee, EmployeesNewEstatesImporterService>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Employee)}s import...");

        var newEstatesCompany = await Mapper
            .ProjectTo<CompaniesImporterResponseModel>(DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == NewEstatesTenantName))
            .FirstOrDefaultAsync();

        if (newEstatesCompany is null)
            throw new InvalidOperationException($"Tenant with name '{CWFortonTenantName}' not found");

        var bulgariaDivisionId = await DbContext
            .Divisions
            .Where(d =>
                d.Name == "България" &&
                d.TenantId == newEstatesCompany.TenantId &&
                d.CompanyId == newEstatesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (bulgariaDivisionId is 0) throw new InvalidOperationException("Division with name 'България' not found");

        var newConstructionDepartmentId = await DbContext
            .Departments
            .AsNoTracking()
            .Where(d =>
                d.Name == "Ново Строителство" &&
                d.TenantId == newEstatesCompany.TenantId &&
                d.CompanyId == newEstatesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        var administrationDepartmentId = await DbContext
            .Departments
            .AsNoTracking()
            .Where(d =>
                d.Name == "Администрация" &&
                d.TenantId == newEstatesCompany.TenantId &&
                d.CompanyId == newEstatesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (administrationDepartmentId is 0 || newConstructionDepartmentId is 0)
            throw new InvalidOperationException("One or more Departments not found");

        var teamNikolaiId = await DbContext
            .Teams
            .AsNoTracking()
            .Where(d =>
                d.Name == "Екипа на Николай Ставрев" &&
                d.TenantId == newEstatesCompany.TenantId &&
                d.CompanyId == newEstatesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        var teamDoraId = await DbContext
            .Teams
            .AsNoTracking()
            .Where(d =>
                d.Name == "Екипа на Дора Керемидчиева" &&
                d.TenantId == newEstatesCompany.TenantId &&
                d.CompanyId == newEstatesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        var teamIvanId = await DbContext
            .Teams
            .AsNoTracking()
            .Where(d =>
                d.Name == "Екипа на Иван Козаров" &&
                d.TenantId == newEstatesCompany.TenantId &&
                d.CompanyId == newEstatesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (teamIvanId is 0 || teamDoraId is 0 || teamNikolaiId is 0)
        {
            throw new InvalidOperationException("One or more Teams not found");
        }

        var officeId = await DbContext
            .Offices
            .AsNoTracking()
            .Where(d =>
                d.Name == "New Estates Патриарха" &&
                d.TenantId == newEstatesCompany.TenantId &&
                d.CompanyId == newEstatesCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (officeId is 0)
        {
            throw new InvalidOperationException("Office with name 'New Estates Патриарха' not found");
        }

        var administratorRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Admin)
            .Where(r => r.TenantId == newEstatesCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var managerRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Manager)
            .Where(r => r.TenantId == newEstatesCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var consultantRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Consultant)
            .Where(r => r.TenantId == newEstatesCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var assistantConsultantRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.AssistantConsultant)
            .Where(r => r.TenantId == newEstatesCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var supervisorRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Supervisor)
            .Where(r => r.TenantId == newEstatesCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        const string managerEmailAddress = "<EMAIL>";

        var employees = new List<EmployeeHardcodedImportModel>
        {
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Весела",
                LastName = "Илиева",
                DisplayName = "Весела",
                PhoneNumber = "+359888805318",
                WorkPosition = "Управител",
                RoleIds = [administratorRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                MappingId = "9193cf59-6ba2-4a6e-b2c1-addb00e4db14",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Красимир",
                LastName = "Джамбазов",
                DisplayName = "Красимир",
                PhoneNumber = "+359882253979",
                WorkPosition = "Търговски директор",
                RoleIds = [managerRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Весела Илиева",
                MappingId = "5b8ea42e-73f8-4585-b8ee-addb00e5f7ed",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Дора",
                LastName = "Керемидчиева",
                DisplayName = "Дора",
                PhoneNumber = "+359884173015",
                WorkPosition = "Мениджър екип",
                RoleIds = [managerRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "e20b51c6-0d3c-44c9-ac90-addb00e62b8e",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Николай",
                LastName = "Ставрев",
                DisplayName = "Николай",
                PhoneNumber = "+359888705608",
                WorkPosition = "Мениджър екип",
                RoleIds = [managerRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "0406d083-bc91-4695-b05c-addb00e6588f",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ирина",
                LastName = "Насева",
                DisplayName = "Ирина",
                PhoneNumber = "+359882951701",
                WorkPosition = "Експерт бизнес развитие",
                RoleIds = [supervisorRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "a0816e4c-d8d6-461e-9247-ae2800d704d3",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Димо",
                LastName = "Димов",
                DisplayName = "Димо",
                PhoneNumber = "+359882552070",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamNikolaiId,
                ManagerName = "Николай Ставрев",
                MappingId = "7157e7eb-83fa-4445-8878-ae2800d873e9",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ина",
                LastName = "Симеонова",
                DisplayName = "Ина",
                PhoneNumber = "+359884259223",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamNikolaiId,
                ManagerName = "Николай Ставрев",
                MappingId = "d92948ea-3905-4261-93ed-aecb00898203",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Славина",
                LastName = "Ангелова",
                DisplayName = "Славина",
                PhoneNumber = "+359885772533",
                WorkPosition = "Кол център агент",
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "940b03a7-4f36-41e9-8684-aeef00956ea7",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ивона",
                LastName = "Стоянова",
                DisplayName = "Ивона",
                PhoneNumber = "+359886101509",
                WorkPosition = "Координатор проекти",
                RoleIds = [assistantConsultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "04a180f7-a727-456c-befc-af04008a4c3a",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Антония",
                LastName = "Чукова",
                DisplayName = "Антония",
                PhoneNumber = "+359882809605",
                WorkPosition = "Кол център агент",
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "ba1e3ac6-2fe2-4851-82e5-af8e00ffdf07",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Аделина",
                LastName = "Коджаманова",
                DisplayName = "Аделина",
                PhoneNumber = "+359884122102",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamDoraId,
                ManagerName = "Дора Керемидчиева",
                MappingId = "d20edd0b-e597-45c0-b789-afd200f2912c",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Иван",
                LastName = "Козаров",
                DisplayName = "Иван",
                PhoneNumber = "+359882422993",
                WorkPosition = "Мениджър екип",
                RoleIds = [managerRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "5d0f7b6c-3b1e-4954-adf6-affd00734045",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Веселина",
                LastName = "Рангелова",
                DisplayName = "Веселина",
                PhoneNumber = "+359888254029",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamIvanId,
                ManagerName = "Иван Козаров",
                MappingId = "b38a9cc0-995e-4848-9850-b02100cafdd1",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Венка",
                LastName = "Пенкова",
                DisplayName = "Венка",
                PhoneNumber = "+359886111031",
                WorkPosition = "Юристконсулт",
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "b6d25e87-9441-4266-9a04-b0a700abd97d",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Яна",
                LastName = "Цачева",
                DisplayName = "Яна",
                PhoneNumber = "+359882661326",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamNikolaiId,
                ManagerName = "Николай Ставрев",
                MappingId = "7c0314df-cba0-4520-a6b0-b0b200858a55",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Борис",
                LastName = "Колев",
                DisplayName = "Борис",
                PhoneNumber = "+359887407062",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamDoraId,
                MappingId = "35a37d7a-8f56-4ad2-85f4-b0b600e47bd2",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Надя",
                LastName = "Драгомирова",
                DisplayName = "Надя",
                PhoneNumber = "+359882385094",
                WorkPosition = "PM - Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                MappingId = "d7781054-4185-4840-8951-b16a007fe60c",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Теодора",
                LastName = "Добрева",
                DisplayName = "Теодора",
                PhoneNumber = "+359882868230",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamNikolaiId,
                ManagerName = "Николай Ставрев",
                MappingId = "87c84205-c079-4f7a-a180-b16c008a0fb4",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Александра",
                LastName = "Милева",
                DisplayName = "Александра",
                PhoneNumber = "+359884755276",
                WorkPosition = "Консултант бивш служител",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "5f67d5ba-b411-4acb-8f95-b17201233db5",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Елиза",
                LastName = "Николаева",
                DisplayName = "Елиза",
                PhoneNumber = "+359884754026",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamDoraId,
                ManagerName = "Дора Керемидчиева",
                MappingId = "9c0030dd-7de4-4f53-a917-b1720123d053",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Габриела",
                LastName = "Караколева",
                DisplayName = "Габриела",
                PhoneNumber = "+359884754340",
                WorkPosition = "Консултант бивш служител",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "899022ac-6f97-4c63-a6d7-b1720124f062",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ивайло",
                LastName = "Ганчев",
                DisplayName = "Ивайло",
                PhoneNumber = "+359885649701",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamIvanId,
                ManagerName = "Иван Козаров",
                MappingId = "ffaf1061-b8d4-481f-88d9-b18600af73dc",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Никола",
                LastName = "Рашков",
                DisplayName = "Никола",
                PhoneNumber = "+359882030553",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamIvanId,
                ManagerName = "Иван Козаров",
                MappingId = "a2d58376-0277-45d0-a070-b1a100a136ae",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Николай",
                LastName = "Стоянов",
                DisplayName = "Николай",
                PhoneNumber = "+359885922410",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamIvanId,
                ManagerName = "Иван Козаров",
                MappingId = "833493a8-36f3-4f64-9e62-b1e00085168d",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Валерия",
                LastName = "Иванова",
                DisplayName = "Валерия",
                PhoneNumber = "+359882377510",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamDoraId,
                ManagerName = "Дора Керемидчиева",
                MappingId = "e9e526d5-6149-4c8b-9bf4-b1e1009ddab1",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Теодора",
                LastName = "Тодорова",
                DisplayName = "Теодора",
                PhoneNumber = "+359884919841",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamNikolaiId,
                ManagerName = "Николай Ставрев",
                MappingId = "fc18581c-36f2-4ea9-98ed-b1f10074e696",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Десислава",
                LastName = "Касабова",
                DisplayName = "Десислава",
                PhoneNumber = "+359882714048",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamDoraId,
                ManagerName = "Дора Керемидчиева",
                MappingId = "b2c413bb-b191-4208-b89d-b20d00bfc87a",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Антонио",
                LastName = "Банов",
                DisplayName = "Антонио",
                PhoneNumber = "+359882836781",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamNikolaiId,
                ManagerName = "Николай Ставрев",
                MappingId = "4815b921-b321-4a7c-85ef-b21b009365a2",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Рада",
                LastName = "Еринина",
                DisplayName = "Рада",
                PhoneNumber = "+359884755900",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamNikolaiId,
                ManagerName = "Николай Ставрев",
                MappingId = "",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ива",
                LastName = "Шъкова",
                DisplayName = "Ива",
                PhoneNumber = "+359882417088",
                WorkPosition = "Кол център агент",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "a81bfaa6-da02-4723-95cd-b1f10075da6f",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Мария",
                LastName = "Блажева",
                DisplayName = "Мария",
                PhoneNumber = "+359884708623",
                WorkPosition = "Кол център агент",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                MappingId = "5bd31a1c-7f8b-4186-8130-afa10107141c",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ива",
                LastName = "Зарева",
                DisplayName = "Ива",
                PhoneNumber = "+359882952702",
                WorkPosition = "Кол център мениджър",
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = administrationDepartmentId,
                DivisionId = bulgariaDivisionId,
                ManagerName = "Красимир Джамбазов",
                OfficeId = officeId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Теодора",
                LastName = "Муцанкиева",
                DisplayName = "Теодора",
                PhoneNumber = "+359885922469",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamIvanId,
                ManagerName = "Иван Козаров",
                OfficeId = officeId,
                MappingId = "f49aef44-18fc-48bd-a8c2-b29f00a08963",
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Весела",
                LastName = "Карачорова",
                DisplayName = "Весела",
                PhoneNumber = "+359884754340",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamIvanId,
                ManagerName = "Иван Козаров",
                OfficeId = officeId,
                MappingId = "70b240fd-29e2-42ac-8741-b2aa00b25971",
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Славена",
                LastName = "Славова",
                DisplayName = "Славена",
                PhoneNumber = "+359884755276",
                WorkPosition = "Консултант",
                RoleIds = [consultantRoleId],
                CompanyId = newEstatesCompany.Id,
                TenantId = newEstatesCompany.TenantId,
                DepartmentId = newConstructionDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamDoraId,
                ManagerName = "Дора Керемидчиева",
                OfficeId = officeId,
                MappingId = "597417b4-6de5-45d4-a238-b2d400c37a75",
            }
        };

        var usersWithEmployees = Mapper
            .Map<List<UserWithEmployeeCreateRequestModel>>(employees)
            .ToList();

        var existingEmails = await DbContext.Employees
            .AsNoTracking()
            .Where(e => e.CompanyId == newEstatesCompany.Id)
            .Include(e => e.UserAccount)
            .Select(e => e.UserAccount.EmailAddress.ToLower())
            .ToListAsync();

        var newEmployeeEmails = new List<string>();

        foreach (var userWithEmployee in usersWithEmployees)
        {
            var email = userWithEmployee.User.EmailAddress?.ToLower();
            if (string.IsNullOrWhiteSpace(email) || existingEmails.Contains(email))
            {
                log.Write($"Skipped existing employee: {email}");
                continue;
            }

            await employeesAppService.CreateAsync(userWithEmployee);
            newEmployeeEmails.Add(email!);
        }

        var savedEmployees = await DbContext.Employees
            .AsNoTracking()
            .Where(e => e.CompanyId == newEstatesCompany.Id)
            .Include(e => e.UserAccount)
            .ToListAsync();

        var mappingList = new List<EmployeeMapping>();

        foreach (var hardCodedEmployee in employees)
        {
            var email = hardCodedEmployee.Email?.ToLower();
            if (!newEmployeeEmails.Contains(email))
            {
                continue;
            }

            var correspondingEmployee =
                savedEmployees.FirstOrDefault(e => e.UserAccount.EmailAddress.ToLower() == email);

            if (!string.IsNullOrWhiteSpace(hardCodedEmployee.ManagerName))
            {
                SetManagerId(savedEmployees, hardCodedEmployee, correspondingEmployee!);
            }

            if (!string.IsNullOrWhiteSpace(hardCodedEmployee.MappingId))
            {
                mappingList.Add(new EmployeeMapping
                {
                    EmployeeId = correspondingEmployee!.Id,
                    EaId = hardCodedEmployee.MappingId
                });
            }
        }

        await DbContext.EmployeeMappings.AddRangeAsync(mappingList);
        await DbContext.SaveChangesAsync();

        log.Write($"Finished New Estates {nameof(Employee)}s import.");

        return employees.Count;
    }

    private static void SetManagerId(List<Employee> savedEmployees, EmployeeHardcodedImportModel hardCodedEmployee,
        Employee correspondingEmployee)
    {
        var veselaId = savedEmployees.FirstOrDefault(e => e.UserAccount.EmailAddress == "<EMAIL>")?.Id;
        var krasimirId = savedEmployees.FirstOrDefault(e => e.UserAccount.EmailAddress == "<EMAIL>")
            ?.Id;
        var nikolaiId = savedEmployees.FirstOrDefault(e => e.UserAccount.EmailAddress == "<EMAIL>")?.Id;
        var ivanId = savedEmployees.FirstOrDefault(e => e.UserAccount.EmailAddress == "<EMAIL>")?.Id;
        var doraId = savedEmployees.FirstOrDefault(e => e.UserAccount.EmailAddress == "<EMAIL>")
            ?.Id;

        switch (hardCodedEmployee.ManagerName)
        {
            case "Весела Илиева":
                correspondingEmployee.ManagerId = veselaId;
                break;
            case "Красимир Джамбазов":
                correspondingEmployee.ManagerId = krasimirId;
                break;
            case "Николай Ставрев":
                correspondingEmployee.ManagerId = nikolaiId;
                break;
            case "Дора Керемидчиева":
                correspondingEmployee.ManagerId = doraId;
                break;
            case "Иван Козаров":
                correspondingEmployee.ManagerId = ivanId;
                break;
        }
    }
}