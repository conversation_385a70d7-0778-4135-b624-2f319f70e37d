namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Clients;
using Companies;
using Extensions;
using Mappings;
using Mappings.DataResolvers.Clients;
using Mappings.DataResolvers.Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Nomenclatures;
using Tags;
using Extensions = RealtoCrm.Extensions.EnumerableExtensions;

public class RelatedClientsNewEstatesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<ClientsUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Client, ClientsUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/new-estates-clients";

    protected override void DecorateBefore(List<Client> entityList, IEnumerable<ClientsUesImportModel> batchAsiData)
    {
        var clientMappings = this.DbContext.ClientMappings
            .Where(c => c.AdminId != null)
            .Include(x => x.Client)
            .ToList();

        entityList.ForEach(c =>
        {
            var correspondingModel = batchAsiData.First(b => b.ClientUesId == c.ClientMapping!.AdminId);

            if (correspondingModel.ClientRelations is null || correspondingModel.ClientRelations.Count == 0)
            {
                return;
            }

            if (correspondingModel.ClientRelations.Count > 0)
            {
                correspondingModel.ClientRelations.ForEach(cr =>
                {
                    if (cr.TypeId is null)
                    {
                        return;
                    }

                    // The mapping which is related to the client in which related clients will be updated.
                    var clientMapping = clientMappings.FirstOrDefault(cm => cm.AdminId == cr.ClientRelationId);

                    if (clientMapping is null)
                    {
                        return;
                    }

                    //The client which is related to the current client.
                    var relatedClient = clientMappings.FirstOrDefault(cm => cm.AdminId == cr.ClientId);

                    if (relatedClient is null)
                    {
                        return;
                    }

                    c.RelatedClients.Add(new RelatedClient
                    {
                        ClientId = clientMapping.ClientId,
                        ClientRelatedClientId = relatedClient.ClientId,
                        RelatedClientTypeId =
                            ClientRelationTypeUesImporterMappings.UesRelationTypeToAsiRelationType[cr.TypeId.Value]
                    });
                });
            }
        });
    }

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(RelatedClient)} import...");

        var count = 0;
        var pageNumber = 1;

        Console.WriteLine($"Fetching initial page for entity: {nameof(RelatedClient)}");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);

        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < BatchSize ? remainingPages : BatchSize;

            var batchData = await Extensions.ToListAsync(this.GetEntitiesForPages(pageNumber, currentBatchSize));

            if (batchData.Count <= 0)
            {
                break;
            }

            batchData = this.FilterAsiData(batchData);

            var mappingDependencies = await this.GetMappingDependencies();

            var batch = this.Mapper
                .Map<List<Client>>(batchData, opt =>
                    mappingDependencies.ForEach(x => opt.Items[x.Key] = x.Value))
                .ToList();

            this.DecorateBefore(batch, batchData);

            var relatedClientsToAdd = batch.SelectMany(x => x.RelatedClients).ToList();

            await this.DbContext.RelatedClients.AddRangeAsync(relatedClientsToAdd);
            await this.DbContext.SaveChangesAsync();

            count += batch.Count;
            pageNumber += currentBatchSize;

            log.Write("Fetched count: " + relatedClientsToAdd.Count);
        }

        log.Write($"Finished {nameof(RelatedClient)} import.");
        return count;
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var tagCategories = await this.DbContext.Set<TagCategory>().ToListAsync();

        var tags = await this.DbContext.Set<Tag>().ToListAsync();

        var company = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == NewEstatesCompanyName)
            .SingleAsync();

        var jobPositions = await this.DbContext.Set<JobPosition>().ToListAsync();

        var workplaces = await this.DbContext.Set<Workplace>().ToListAsync();

        var clientPreferences = await this.DbContext.Set<ClientPreference>().ToListAsync();

        var nationalities = await this.DbContext.Set<Nationality>().ToListAsync();

        var titles = await this.DbContext.Set<Title>().ToListAsync();

        var maritalStatuses = await this.DbContext.Set<MaritalStatus>().ToListAsync();

        var sectorMappings = this.DbContext.SectorMappings.AsNoTracking().ToList();

        var municipalityMappings = this.DbContext.MunicipalityMappings
            .Include(m => m.Municipality).AsNoTracking().ToList();

        return new Dictionary<string, object>
        {
            { ClientTagsValueResolver.TagCategoriesItemKey, tagCategories },
            { ClientTagsValueResolver.ExistingTagsItemKey, tags },
            { ClientJobPositionsValueResolver.JobPositionsItemsKey, jobPositions },
            { TenantValueResolver<ClientsUesImportModel, Client>.CompanyNameItemsKey, company },
            { ClientWorkplaceValueResolver.WorkplacesItemsKey, workplaces },
            { ClientPreferencesValueResolver.ClientPreferencesItemsKey, clientPreferences },
            { NationalityValueResolver.NationalitiesItemsKey, nationalities },
            { TitleValueResolver.TitlesItemsKey, titles },
            { MaritalStatusesValueResolver.MaritalStatusesItemsKey, maritalStatuses },
            { ClientSectorsValueResolver.SectorMappingsItemsKey, sectorMappings },
            { DocumentAuthorityValueResolver.MunicipalityMappingsItemsKey, municipalityMappings },
        };
    }
}