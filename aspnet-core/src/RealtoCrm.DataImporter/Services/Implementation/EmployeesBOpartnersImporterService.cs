namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Authorization.Roles;
using AutoMapper;
using Employees;
using Employees.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;
using static CosherConsts.Tenants;
using static CosherConsts.Departments;
using static CosherConsts.Divisions;
using static CosherConsts.Offices;
using static CosherConsts.Teams;

public class EmployeesBOpartnersImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IEmployeesAppService employeesAppService,
    IJsonSerializerService<EmployeesBOpartnersImporterService> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Employee, EmployeesBOpartnersImporterService>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started BOpartners {nameof(Employee)}s import...");

        var BOpartnersCompany = await Mapper
            .ProjectTo<CompaniesImporterResponseModel>(DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == BOPartnersTenantName))
            .FirstOrDefaultAsync();

        if (BOpartnersCompany is null)
            throw new InvalidOperationException($"Tenant with name '{BOPartnersTenantName}' not found");

        var bulgariaDivisionId = await DbContext
            .Divisions
            .Where(d =>
                d.Name == BulgariaDivisionName &&
                d.TenantId == BOpartnersCompany.TenantId &&
                d.CompanyId == BOpartnersCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (bulgariaDivisionId is 0) throw new InvalidOperationException($"Division with name '{BulgariaDivisionName}' not found");

        var departmentNames = new[]
        {
            ITDepartmentName,
            MarketingDepartmentName,
            LegalDepartmentName,
            HRDepartmentName,
            FinanceDepartmentName
        };

        var departmentIds = new Dictionary<string, int>();

        foreach (var departmentName in departmentNames)
        {
            var id = await this.DbContext.Departments
                .AsNoTracking()
                .Where(d =>
                    d.Name == departmentName &&
                    d.TenantId == BOpartnersCompany.TenantId &&
                    d.CompanyId == BOpartnersCompany.Id)
                .Select(d => d.Id)
                .FirstOrDefaultAsync();

            if (id == 0)
            {
                throw new InvalidOperationException($"Department with name '{departmentName}' not found");
            }

            departmentIds[departmentName] = id;
        }

        var teamNames = new[]
        {
            ITSupportTeamName
        };

        var teamIds = new Dictionary<string, int>();

        foreach (var teamName in teamNames)
        {
            var id = await this.DbContext.Teams
                .AsNoTracking()
                .Where(t =>
                    t.Name == teamName &&
                    t.TenantId == BOpartnersCompany.TenantId &&
                    t.CompanyId == BOpartnersCompany.Id)
                .Select(t => t.Id)
                .FirstOrDefaultAsync();

            if (id == 0)
            {
                throw new InvalidOperationException($"Team with name '{teamName}' not found");
            }

            teamIds[teamName] = id;
        }

        var officeSofiaDondukovId = await DbContext
            .Offices
            .AsNoTracking()
            .Where(d =>
                d.Name == BOpartnersCentralOfficeName &&
                d.TenantId == BOpartnersCompany.TenantId &&
                d.CompanyId == BOpartnersCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (officeSofiaDondukovId is 0)
        {
            throw new InvalidOperationException($"Office with name '{BOpartnersCentralOfficeName}' not found");
        }

        // var officeSofiaTrapezitcaName = "Касата Трапезица";
        // var officeSofiaTrapezitcaId = await DbContext
        //     .Offices
        //     .AsNoTracking()
        //     .Where(d =>
        //         d.Name == officeSofiaTrapezitcaName &&
        //         d.TenantId == BOpartnersCompany.TenantId &&
        //         d.CompanyId == BOpartnersCompany.Id)
        //     .Select(d => d.Id)
        //     .FirstOrDefaultAsync();
        //
        // if (officeSofiaTrapezitcaId is 0)
        // {
        //     throw new InvalidOperationException($"Office with name '{officeSofiaTrapezitcaName}' not found");
        // }

        var officeVarnaId = await DbContext
            .Offices
            .AsNoTracking()
            .Where(d =>
                d.Name == BOpartnersVarnaOfficeName &&
                d.TenantId == BOpartnersCompany.TenantId &&
                d.CompanyId == BOpartnersCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (officeVarnaId is 0)
        {
            throw new InvalidOperationException($"Office with name '{BOpartnersVarnaOfficeName}' not found");
        }

        var administratorRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Admin)
            .Where(r => r.TenantId == BOpartnersCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var managerRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Manager)
            .Where(r => r.TenantId == BOpartnersCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var unallocatedRoleId = await DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Unallocated)
            .Where(r => r.TenantId == BOpartnersCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var employees = new List<EmployeeHardcodedImportModel>
        {
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Александра",
                MiddleName = "Василева",
                LastName = "Лехчанска",
                DisplayName = "Алекс",
                PhoneNumber = "+359885467203",
                WorkPosition = "Специалист дигитален маркетинг и реклама",
                ManagerName = "Милен Щерев",
                RoleIds = [managerRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[MarketingDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Бахтишен",
                MiddleName = "Хюсеинова",
                LastName = "Мехмедова-Агер",
                DisplayName = "Бахтишен",
                PhoneNumber = "+359885603580",
                WorkPosition = "Касиер, счетоводство",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeVarnaId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName]
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Борис",
                MiddleName = "Ангелов",
                LastName = "Кожухаров",
                DisplayName = "Боре",
                PhoneNumber = "+359887740488",
                WorkPosition = "Системен администратор",
                ManagerName = "Михаил Илинов",
                RoleIds = [administratorRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
                TeamId = teamIds[ITSupportTeamName]
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Боряна",
                MiddleName = "Христова",
                LastName = "Деспотова-Шали",
                DisplayName = "Боряна",
                PhoneNumber = "+********** 435",
                WorkPosition = "Счетоводител, финансист",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Владимир",
                MiddleName = "Нейчев",
                LastName = "Кръстев",
                DisplayName = "Влади",
                PhoneNumber = "+359888044755",
                WorkPosition = "Системен администратор",
                ManagerName = "Михаил Илинов",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeVarnaId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Галина",
                MiddleName = "Захариева",
                LastName = "Стефанова",
                DisplayName = "Галя",
                PhoneNumber = "+********** 975",
                WorkPosition = "Главен счетоводител",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Деяна",
                MiddleName = "Николова",
                LastName = "Тенева",
                DisplayName = "Деяна",
                PhoneNumber = "+********** 114",
                WorkPosition = "Главен счетоводител",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Димитричка",
                MiddleName = "Георгиева",
                LastName = "Минчева",
                DisplayName = "Дими",
                PhoneNumber = "+359882313618",
                WorkPosition = "Юрисконсулт",
                ManagerName = "Снежана Тодорова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[LegalDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Драгомир",
                MiddleName = "Николов",
                LastName = "Стайков",
                DisplayName = "Драго",
                PhoneNumber = "+********** 667",
                WorkPosition = "Юрисконсулт",
                ManagerName = "Снежана Тодорова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[LegalDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Евангелина",
                MiddleName = "Райчева",
                LastName = "Радосова",
                DisplayName = "Евангелина",
                PhoneNumber = "+359882905589",
                WorkPosition = "Експерт маркетинг",
                ManagerName = "Милен Щерев",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[MarketingDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Елена",
                MiddleName = "Валентиновна",
                LastName = "Маркова",
                DisplayName = "Елена",
                PhoneNumber = "+359885922406",
                WorkPosition = "Касиер счетоводство",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Елина",
                MiddleName = "Георгиева",
                LastName = "Брандийска",
                DisplayName = "Елина",
                PhoneNumber = "+359885922412",
                WorkPosition = "Специалист труд и работна заплата",
                ManagerName = "Стефка Николова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[HRDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Иван",
                MiddleName = "Христов",
                LastName = "Друнчилов",
                DisplayName = "Иван",
                PhoneNumber = "+359882712047",
                WorkPosition = "Финансов контрольор",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ивон",
                MiddleName = "Николаева",
                LastName = "Милева",
                DisplayName = "Ивон",
                PhoneNumber = "+359888378271",
                WorkPosition = "Мениджър проекти конференции и събития",
                ManagerName = "Милен Щерев",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[MarketingDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Ирина",
                MiddleName = "Вергилова",
                LastName = "Колева",
                DisplayName = "Ирина",
                PhoneNumber = "+359884049522",
                WorkPosition = "Касиер счетоводство",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Йоана",
                MiddleName = "Наскова",
                LastName = "Василева",
                DisplayName = "Йоана",
                PhoneNumber = "+359887385530",
                WorkPosition = "Разработчик софтуер",
                ManagerName = "Михаил Илинов",
                RoleIds = [administratorRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Йордан",
                MiddleName = "Георгиев",
                LastName = "Коларов",
                DisplayName = "Йордан",
                PhoneNumber = "+359882421441",
                WorkPosition = "Старши юрисконсулт",
                ManagerName = "Снежана Тодорова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeVarnaId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[LegalDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Камелия",
                MiddleName = "Емилова",
                LastName = "Иванова",
                DisplayName = "Камелия",
                PhoneNumber = "+359885115221",
                WorkPosition = "Счетоводител, оперативен",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Константин",
                MiddleName = "Димитров",
                LastName = "Генчев",
                DisplayName = "Константин",
                PhoneNumber = "+359884889299",
                WorkPosition = "Старши разработчик софтуер",
                ManagerName = "Михаил Илинов",
                RoleIds = [administratorRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Красимира",
                MiddleName = "Атанасова",
                LastName = "Котларова",
                DisplayName = "Красимира",
                PhoneNumber = "+359885115815",
                WorkPosition = "Счетоводител отчетник",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Красимира",
                MiddleName = "Димова",
                LastName = "Милева",
                DisplayName = "Красимира",
                PhoneNumber = "+359889090005",
                WorkPosition = "Счетоводител отчетник",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeVarnaId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Кристина",
                MiddleName = "Иванова",
                LastName = "Микова",
                DisplayName = "Кристина",
                PhoneNumber = "+359885034868",
                WorkPosition = "Асистент на управителя",
                ManagerName = "Пенка Димитрова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Лиляна",
                MiddleName = "Георгиева",
                LastName = "Георгиева",
                DisplayName = "Лиляна",
                PhoneNumber = "+359888760465",
                WorkPosition = "Мениджър на търговската марка Бранд мениджър",
                ManagerName = "Милен Щерев",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[MarketingDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Лиляна",
                MiddleName = "Дечкова",
                LastName = "Исова",
                DisplayName = "Лиляна",
                PhoneNumber = "+359885922411",
                WorkPosition = "Счетоводител отчетник",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Магдалена",
                MiddleName = "Неделчева",
                LastName = "Великова",
                DisplayName = "Магдалена",
                PhoneNumber = "+359882299059",
                WorkPosition = "Бизнес анализатор информационни технологии",
                ManagerName = "Михаил Илинов",
                RoleIds = [administratorRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Милен",
                MiddleName = "Георгиев",
                LastName = "Щерев",
                DisplayName = "Милен",
                PhoneNumber = "+359895495498",
                WorkPosition = "Маркетинг мениджър",
                ManagerName = "Пенка Димитрова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[MarketingDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Милена",
                MiddleName = "Дункова",
                LastName = "Гълъбова",
                DisplayName = "Милена",
                PhoneNumber = "+359885922457",
                WorkPosition = "Експерт труд и работна заплата",
                ManagerName = "Стефка Николова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[HRDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Мирослав",
                MiddleName = "Стефков",
                LastName = "Василев",
                DisplayName = "Мирослав",
                PhoneNumber = "+359885601677",
                WorkPosition = "Главен счетоводител",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Михаил",
                MiddleName = "Пламенов",
                LastName = "Илинов",
                DisplayName = "Михаил",
                PhoneNumber = "+359884391009",
                WorkPosition = "Директор софтуерно развитие",
                ManagerName = "Пенка Димитрова",
                RoleIds = [administratorRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Надя",
                MiddleName = "Сталева",
                LastName = "Жекова",
                DisplayName = "Надя",
                PhoneNumber = "+359885922648",
                WorkPosition = "Счетоводител отчетник",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Наталия",
                MiddleName = "Сталева",
                LastName = "Владова-Йорданова",
                DisplayName = "Наталия",
                PhoneNumber = "+359884810980",
                WorkPosition = "Отчетник финанси",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Пенка",
                MiddleName = "Петрова",
                LastName = "Димитрова",
                DisplayName = "Пенка",
                PhoneNumber = "+359888009333",
                WorkPosition = "Управител",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Светлана",
                MiddleName = "Иванова",
                LastName = "Петрова",
                DisplayName = "Светлана",
                PhoneNumber = "+359887762720",
                WorkPosition = "Старши експерт финансов супервайзър",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Симеон",
                MiddleName = "Димитров",
                LastName = "Симеонов",
                DisplayName = "Симеон",
                PhoneNumber = "+359882171670",
                WorkPosition = "Техник поддръжка на компютри",
                ManagerName = "Михаил Илинов",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Симеон",
                MiddleName = "Стоянов",
                LastName = "Стоянов",
                DisplayName = "Симеон",
                PhoneNumber = "+359884399523",
                WorkPosition = "Техник поддръжка на компютри",
                ManagerName = "Михаил Илинов",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Стефка",
                MiddleName = "Методиева",
                LastName = "Николова",
                DisplayName = "Стефка",
                PhoneNumber = "+359886635333",
                WorkPosition = "Мениджър Възнаграждения и администриране на персонала",
                ManagerName = "Пенка Димитрова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[HRDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Стоян",
                MiddleName = "Димитров",
                LastName = "Димитров",
                DisplayName = "Стоян",
                PhoneNumber = "+359887001096",
                WorkPosition = "Юрисконсулт",
                ManagerName = "Снежана Тодорова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[LegalDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Татяна",
                MiddleName = "Генчева",
                LastName = "Андреева",
                DisplayName = "Татяна",
                PhoneNumber = "+359884399760",
                WorkPosition = "Счетоводител отчетник",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Теодора",
                MiddleName = "Николаева",
                LastName = "Казанджиева-Иванова",
                DisplayName = "Теодора",
                PhoneNumber = "+359888664741",
                WorkPosition = "Бизнес анализатор информационни технологии",
                ManagerName = "Михаил Илинов",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeVarnaId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Теодора",
                MiddleName = "Стоева",
                LastName = "Стоева",
                DisplayName = "Теодора",
                PhoneNumber = "+359882032604",
                WorkPosition = "Ръководител ИТ проекти",
                ManagerName = "Михаил Илинов",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Тодор",
                MiddleName = "Кузманов",
                LastName = "Велев",
                DisplayName = "Тодор",
                PhoneNumber = "+359882941178",
                WorkPosition = "Продуктов мениджър",
                ManagerName = "Михаил Илинов",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Трендафила",
                MiddleName = "Иванова",
                LastName = "Кьосева",
                DisplayName = "Трендафила",
                PhoneNumber = "+********** 022",
                WorkPosition = "Юрисконсулт",
                ManagerName = "Снежана Тодорова",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[LegalDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Христина",
                MiddleName = "Петрова",
                LastName = "Петрова",
                DisplayName = "Христина",
                PhoneNumber = "+********** 018",
                WorkPosition = "Счетоводител финансист",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[FinanceDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Цеца",
                MiddleName = "Тодорова",
                LastName = "Борисова",
                DisplayName = "Цеца",
                PhoneNumber = "+359885922570",
                WorkPosition = "Представител бизнес услуги интернет маркетинг",
                ManagerName = "Милен Щерев",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[MarketingDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Снежана",
                MiddleName = "",
                LastName = "Тодорова",
                DisplayName = "Юлия",
                PhoneNumber = "+359885922563\n",
                WorkPosition = "Ръководител Правен Отдел",
                ManagerName = "Снежана Тодорова",
                RoleIds = [managerRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeVarnaId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[LegalDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Яна",
                MiddleName = "",
                LastName = "Митева",
                DisplayName = "Яна",
                PhoneNumber = "+359884035628",
                WorkPosition = "Специалист маркетинг",
                ManagerName = "Милен Щерев",
                RoleIds = [unallocatedRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[MarketingDepartmentName],
            },
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Койчо",
                MiddleName = "",
                LastName = "Йовчев",
                DisplayName = "Койчо",
                PhoneNumber = "+359899128914",
                WorkPosition = "Разработчик софтуер",
                ManagerName = "Михаил Илинов",
                RoleIds = [administratorRoleId],
                CompanyId = BOpartnersCompany.Id,
                TenantId = BOpartnersCompany.TenantId,
                OfficeId = officeSofiaDondukovId,
                DivisionId = bulgariaDivisionId,
                DepartmentId = departmentIds[ITDepartmentName],
            },
            
        };

        var usersWithEmployees = Mapper
            .Map<List<UserWithEmployeeCreateRequestModel>>(employees)
            .ToList();

        foreach (var userWithEmployee in usersWithEmployees)
        {
            var exists = await DbContext.Users
                .AsNoTracking()
                .AnyAsync(u => u.TenantId == userWithEmployee.TenantId && u.EmailAddress == userWithEmployee.User.EmailAddress);

            if (exists)
            {
                log.Write($"User with email '{userWithEmployee.User.EmailAddress}' already exists in tenant {BOPartnersTenantName}. Skipping.");
                continue; // skip this one
            }

            await employeesAppService.CreateAsync(userWithEmployee);
        }
        
        await DbContext.SaveChangesAsync();

        var savedEmployees = await DbContext.Employees
            .AsNoTracking()
            .Where(e => e.CompanyId == BOpartnersCompany.Id)
            .Include(e => e.UserAccount)
            .ToListAsync();

        var mappingList = new List<EmployeeMapping>();

        foreach (var hardCodedEmployee in employees)
        {
            var correspondingEmployee =
                savedEmployees.FirstOrDefault(e => e.CompanyId == hardCodedEmployee.TenantId &&
                                                   e.UserAccount.EmailAddress == hardCodedEmployee.Email);

            if (!string.IsNullOrWhiteSpace(hardCodedEmployee.ManagerName))
            {
                this.SetManagerId(hardCodedEmployee, correspondingEmployee!);
            }

            if (!string.IsNullOrWhiteSpace(hardCodedEmployee.MappingId))
                mappingList.Add(new EmployeeMapping
                {
                    EmployeeId = correspondingEmployee!.Id,
                    EaId = hardCodedEmployee.MappingId
                });
        }

        await DbContext.EmployeeMappings.AddRangeAsync(mappingList);
        await DbContext.SaveChangesAsync();

        log.Write($"Finished BOpartners {nameof(Employee)}s import.");

        return employees.Count;
    }

    private void SetManagerId(EmployeeHardcodedImportModel hardCodedEmployee, Employee correspondingEmployee)
    {
        var managerEmailMap = new Dictionary<string, string>
        {
            ["Пенка Димитрова"] = "<EMAIL>",
            ["Милен Щерев"] = "<EMAIL>",
            ["Михаил Илинов"] = "<EMAIL>",
            ["Снежана Тодорова"] = "<EMAIL>",
            ["Стефка Николова"] = "<EMAIL>"
        };

        if (string.IsNullOrWhiteSpace(hardCodedEmployee.ManagerName))
            return;

        if (!managerEmailMap.TryGetValue(hardCodedEmployee.ManagerName, out var managerEmail))
            return;

        var managerId = DbContext.Employees
            .AsNoTracking()
            .Where(e =>
                e.CompanyId == hardCodedEmployee.TenantId &&
                e.UserAccount.EmailAddress == managerEmail)
            .Select(e => e.Id)
            .FirstOrDefault();

        if (managerId != 0)
        {
            correspondingEmployee.ManagerId = managerId;
        }
    }

}