namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Authorization.Roles;
using AutoMapper;
using Employees;
using Employees.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Models.ResponseModels;
using static CosherConsts.Tenants;

public class EmployeesRealtoGroupImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IEmployeesAppService employeesAppService,
    IJsonSerializerService<EmployeesRealtoGroupImporterService> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Employee, EmployeesRealtoGroupImporterService>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => "Not used for now";

    public override async Task<int> Import()
    {
        log.Write($"Started {nameof(Employee)}s import...");

        var realtoCompany = await this.Mapper
            .ProjectTo<CompaniesImporterResponseModel>(this.DbContext
                .Companies
                .AsNoTracking()
                .Where(c => c.Name == RealtoTenantName))
            .FirstOrDefaultAsync();

        if (realtoCompany is null)
        {
            throw new InvalidOperationException($"Tenant with name '{RealtoTenantName}' not found");
        }

        var bulgariaDivisionId = await this.DbContext
            .Divisions
            .Where(d =>
                d.Name == "България" &&
                d.TenantId == realtoCompany.TenantId &&
                d.CompanyId == realtoCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (bulgariaDivisionId is 0)
        {
            throw new InvalidOperationException("Division with name 'България' not found");
        }

        var businessDepartmentId = await this.DbContext
            .Departments
            .AsNoTracking()
            .Where(d =>
                d.Name == "Оперативен бизнес" &&
                d.TenantId == realtoCompany.TenantId &&
                d.CompanyId == realtoCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (businessDepartmentId is 0)
        {
            throw new InvalidOperationException("Department 'Оперативен бизнес' not found");
        }

        var teamZarkovaId = await this.DbContext
            .Teams
            .AsNoTracking()
            .Where(d =>
                d.Name == "Екипа на Гергана Заркова" &&
                d.TenantId == realtoCompany.TenantId &&
                d.CompanyId == realtoCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (teamZarkovaId is 0)
        {
            throw new InvalidOperationException("One or more Teams not found");
        }

        var officeId = await this.DbContext
            .Offices
            .AsNoTracking()
            .Where(d =>
                d.Name == "Реалто централа" &&
                d.TenantId == realtoCompany.TenantId &&
                d.CompanyId == realtoCompany.Id)
            .Select(d => d.Id)
            .FirstOrDefaultAsync();

        if (officeId is 0)
        {
            throw new InvalidOperationException("Office with name 'Реалто централа' not found");
        }

        var supervisorRoleId = await this.DbContext
            .Roles
            .AsNoTracking()
            .Where(r => r.Name == StaticRoleNames.Tenants.Supervisor)
            .Where(r => r.TenantId == realtoCompany.TenantId)
            .Select(r => r.Id)
            .FirstAsync();

        var employees = new List<EmployeeHardcodedImportModel>
        {
            new()
            {
                Username = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Гергана",
                LastName = "Заркова",
                DisplayName = "Гергана",
                PhoneNumber = "+359886607600",
                WorkPosition = "Оперативен директор",
                RoleIds = [supervisorRoleId],
                CompanyId = realtoCompany.Id,
                TenantId = realtoCompany.TenantId,
                OfficeId = officeId,
                DepartmentId = businessDepartmentId,
                DivisionId = bulgariaDivisionId,
                TeamId = teamZarkovaId
            }
        };

        var usersWithEmployees = this.Mapper
            .Map<List<UserWithEmployeeCreateRequestModel>>(employees)
            .ToList();

        var existingEmails = await this.DbContext.Employees
            .AsNoTracking()
            .Where(e => e.CompanyId == realtoCompany.Id)
            .Include(e => e.UserAccount)
            .Select(e => e.UserAccount.EmailAddress.ToLower())
            .ToListAsync();

        foreach (var userWithEmployee in usersWithEmployees)
        {
            var email = userWithEmployee.User.EmailAddress?.ToLower();
            if (string.IsNullOrWhiteSpace(email) || existingEmails.Contains(email))
            {
                log.Write($"Skipped existing employee: {email}");
                continue;
            }

            await employeesAppService.CreateAsync(userWithEmployee);
        }

        await this.DbContext.SaveChangesAsync();

        log.Write($"Finished Realto Group {nameof(Employee)}s import.");

        return employees.Count;
    }
}