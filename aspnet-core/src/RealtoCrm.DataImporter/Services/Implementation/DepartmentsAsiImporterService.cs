namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Companies;
using Models;
using Models.ResponseModels;

public class DepartmentsAsiImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<DepartmentsAsiImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseAsiImporterService<Department, DepartmentsAsiImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/office?office_structure_id=3";

    protected override void DecorateBefore(List<Department> entityList,
        IEnumerable<DepartmentsAsiImportModel> batchAsiData)
    {
        var offices = this.DbContext.Offices.Include(o => o.Address).ToList();

        var divisionsMappings = this.Mapper
            .ProjectTo<DivisionMapResponseModel>(this
                .DbContext
                .DivisionMappings)
            .ToList();

        var populatedPlaceMappings = this.Mapper
            .ProjectTo<PopulatedPlaceMapResponseModel>(this
                .DbContext
                .PopulatedPlaceMappings)
            .ToList();

        var provinceMappings = this.Mapper
            .ProjectTo<ProvinceMapResponseModel>(this
                .DbContext
                .ProvinceMappings)
            .ToList();

        var municipalityMappings = this.Mapper
            .ProjectTo<MunicipalityMapResponseModel>(this
                .DbContext
                .MunicipalityMappings)
            .ToList();

        var districtMappings = this.Mapper
            .ProjectTo<DistrictMapResponseModel>(this
                .DbContext
                .DistrictMappings)
            .ToList();

        var streetMappings = this.Mapper
            .ProjectTo<StreetMapResponseModel>(this
                .DbContext
                .StreetMappings)
            .ToList();

        entityList.ForEach(department =>
        {
            var correspondingModel = batchAsiData.FirstOrDefault(d =>
                d.Name == department.Name);

            if (correspondingModel == null)
            {
                return;
            }

            if (!CanMapDivision(divisionsMappings, correspondingModel, department))
            {
                return;
            }

            MapOffice(municipalityMappings,
                correspondingModel,
                populatedPlaceMappings,
                districtMappings,
                provinceMappings,
                streetMappings,
                offices,
                department);
        });
    }

    protected override IEnumerable<Department> ApplySpecifics(IEnumerable<Department> entities)
    {
        var entitiesToSave = entities
            .Where(x => x.DivisionId != 0);
        
        return entitiesToSave;
    }

    protected override async Task DecorateAfter(IEnumerable<Department> entityList,
        List<DepartmentsAsiImportModel> batchAsiData)
    {
        var namesFromAsi = batchAsiData
            .Select(p => p.Name)
            .ToHashSet();
        var currentDepartments = this.Mapper
            .ProjectTo<ImporterResponseModel>(this
                .DbContext
                .Departments
                .Where(d => namesFromAsi.Contains(d.Name)))
            .ToList();

        var departmentMappings = entityList
            .Select(department =>
            {
                var correspondingDepartment = batchAsiData.FirstOrDefault(o => o.Name == department.Name);
                if (correspondingDepartment == null)
                {
                    return null;
                }

                var currentDepartment = currentDepartments.FirstOrDefault(o => o.Name == department.Name);
                if (currentDepartment == null)
                {
                    return null;
                }

                return new DepartmentMapping
                {
                    DepartmentId = currentDepartment.Id,
                    AsiId = correspondingDepartment.Id
                };
            })
            .Where(mapping => mapping != null)
            .ToList();

        if (departmentMappings.Any())
        {
            await this.DbContext.DepartmentMappings.AddRangeAsync(departmentMappings!);
            await this.DbContext.SaveChangesAsync();
        }
    }

    private static bool CanMapDivision(List<DivisionMapResponseModel> divisionsMappings,
        DepartmentsAsiImportModel correspondingModel,
        Department department)
    {
        var divisionMapping = divisionsMappings.FirstOrDefault(d
            => d.AsiId == correspondingModel.ParentId);

        if (divisionMapping == null)
        {
            return false;
        }

        department.DivisionId = divisionMapping.DivisionId;

        return true;
    }

    private static void MapOffice(
        List<MunicipalityMapResponseModel> municipalityMappings,
        DepartmentsAsiImportModel correspondingModel,
        List<PopulatedPlaceMapResponseModel> populatedPlaceMappings,
        List<DistrictMapResponseModel> districtMappings,
        List<ProvinceMapResponseModel> provinceMappings,
        List<StreetMapResponseModel> streetMappings,
        List<Office> offices,
        Department department)
    {
        var municipalityId = municipalityMappings.FirstOrDefault(m =>
            m.AsiId == correspondingModel.MunicipalityId)?.MunicipalityId ?? null;
        var pleaceId = populatedPlaceMappings.FirstOrDefault(p =>
            p.AsiId == correspondingModel.LocationId)?.PopulatedPlaceId ?? null;
        var districtId = districtMappings.FirstOrDefault(d =>
            d.AsiId == correspondingModel.QuarterId)?.DistrictId ?? null;
        var provinceId = provinceMappings.FirstOrDefault(p =>
            p.AsiId == correspondingModel.RegionId)?.ProvinceId ?? null;
        var streetId = streetMappings.FirstOrDefault(s =>
            s.AsiId == correspondingModel.StreetId)?.StreetId ?? null;

        var office = offices.FirstOrDefault(o =>
            o.Address.PopulatedPlaceId == pleaceId &&
            o.Address.StreetId == streetId &&
            o.Address.DistrictId == districtId &&
            o.Address.MunicipalityId == municipalityId &&
            o.Address.ProvinceId == provinceId);

        if (office == null)
        {
            throw new ArgumentNullException(
                $"No matching office found in the database for {department.Name}.");
        }

        department.OfficeId = office.Id;
    }
}