using System.Net.Http;
using System.Net.Http.Headers;

namespace RealtoCrm.DataImporter.Services.Implementation;

using System.IO;
using System.Text.Encodings.Web;
using System.Threading.Tasks;

public class ImportDocumentFetchService(HttpClient httpClient) : IImportDocumentFetchService
{
    private const string ParentDirectoryName = "realto_import_documents";

    private static readonly string ParentDirectoryPath = Path.Combine(Path.GetTempPath(), ParentDirectoryName);

    static ImportDocumentFetchService()
    {
        if (!Directory.Exists(ParentDirectoryPath))
        {
            Directory.CreateDirectory(ParentDirectoryPath);
        }
    }

    public async Task<string> FetchDocumentAsync(string url, string apiKey)
    {
        var encodedUrl = UrlEncoder.Default.Encode(url);
        var filePath = Path.Combine(ParentDirectoryPath, $"file_{encodedUrl}.json");
        await this.EnsureDocumentIsFetched(url, apiKey, filePath);
        return await File.ReadAllTextAsync(filePath);
    }

    private async Task EnsureDocumentIsFetched(string fetchUrl, string apiKey, string filePath)
    {
        if (File.Exists(filePath))
        {
            return;
        }

        //TODO: put in the token in config
        httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue(apiKey);

        var response = await httpClient.GetAsync(fetchUrl);
        await using var fileStream = File.Create(filePath);
        await response.Content.CopyToAsync(fileStream);
        // TODO: write fetch logic
    }
}