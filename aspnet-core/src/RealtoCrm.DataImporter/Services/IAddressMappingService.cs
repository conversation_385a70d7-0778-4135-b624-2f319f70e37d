namespace RealtoCrm.DataImporter.Services;

using Addresses;
using Models;
using System.Collections.Generic;
using Models.ResponseModels;
using Models.Enums;

public interface IAddressMappingService
{
    Address MapAddress(Address address,
        AddressMappingModel correspondingModel,
        IEnumerable<PopulatedPlaceMapResponseModel> populatedPlaceMappings,
        IEnumerable<ProvinceMapResponseModel> provinceMappings,
        IEnumerable<MunicipalityMapResponseModel> municipalityMappings,
        IEnumerable<DistrictMapResponseModel> districtMappings,
        IEnumerable<StreetMapResponseModel> streetMappings,
        ImporterType importType = ImporterType.Asi);
}