namespace RealtoCrm.DataImporter.Services;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;

public abstract class BaseUesImporterService<TEntity, TModel>(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<TModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseImporterService<TEntity, TModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
    where TEntity : class
    where TModel : class
{
    protected string DefaultUrl => "https://admin.ues.bg/crm-export-api/v0";

    protected override string ApiKeyConfigurationKey => "UesApiKey";

    protected const int UniqueEstatesTenantId = 3;

    protected const string UniqueEstatesCompanyName = "Unique Estates";
    
    protected const string NewEstatesCompanyName = "New Estates";

    private readonly PropertyInfo? _deletedProperty = typeof(TModel).GetProperty("IsDeletedImportEntry");

    //filtering out all deleted entries coming from the endpoint
    //later will be moved in the parent class
    //and filter both asi and ues (issue is logged)
    protected override List<TModel> FilterAsiData(List<TModel> batchAsiData)
    {
        if (_deletedProperty != null)
        {
            return batchAsiData
                .Where(model =>
                {
                    var deletedValue = _deletedProperty.GetValue(model);
                    return deletedValue == null || !Convert.ToInt32(deletedValue).Equals(1);
                })
                .ToList();
        }

        Console.WriteLine($"Warning: {typeof(TModel).Name} does not have a Deleted property. No filtering applied.");
        return base.FilterAsiData(batchAsiData);
    }
}