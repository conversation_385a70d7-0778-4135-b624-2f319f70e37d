namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Offers;
using static RealtoCrm.CosherConsts.DealMotives;

public class DealMotiveValueResolver
    : IValueResolver<OfferUesImportModel, Offer, DealMotive?>,
        IValueResolver<OfferAsiImportModel, Offer, DealMotive>
{
    public const string DealMotivesItemsKey = "dealMotives";

    private readonly Dictionary<int, string> uesDealMotiveIdToDealMotiveName =
        new()
        {
            { 1, LeavingInvestmentDealMotiveName },
            { 2, SplittingDealMotiveName },
            { 3, NeedMoneyDealMotiveName },
            { 4, RepayingCreditDealMotiveName },
            { 5, SellingToBuyAnotherDealMotiveName },
            { 6, ChangingResidenceDealMotiveName },
            { 7, ProductRealizationDealMotiveName },
            { 8, OtherDealMotiveName },
            { 9, ForInvestmentDealMotiveName },
            { 10, ForBusinessDealMotiveName },
            { 11, LivingUnderRentDealMotiveName },
            { 12, BuyingForLivingDealMotiveName },
            { 13, "местене в друг град" },
            { 14, ChangingResidenceDealMotiveName },
            { 15, OtherDealMotiveName },
        };

    private readonly Dictionary<int, string> asiDealMotiveIdToDealMotiveName =
        new()
        {
            { 1, LeavingInvestmentDealMotiveName },
            { 2, SplittingDealMotiveName },
            { 3, NeedMoneyDealMotiveName },
            { 4, RepayingCreditDealMotiveName },
            { 5, SellingToBuyAnotherDealMotiveName },
            { 6, ChangingResidenceDealMotiveName },
            { 7, ProductRealizationDealMotiveName },
            { 8, "друго" },
            { 9, ForInvestmentDealMotiveName },
            { 10, ForBusinessDealMotiveName },
            { 11, LivingUnderRentDealMotiveName },
            { 12, BuyingForLivingDealMotiveName },
            { 13, "местене в друг град" },
            { 14, ChangingResidenceDealMotiveName },
            { 15, OtherDealMotiveName },
        };

    public DealMotive? Resolve(OfferUesImportModel source, Offer destination, DealMotive? destMember, ResolutionContext context)
        => null;

    public DealMotive Resolve(OfferAsiImportModel source, Offer destination, DealMotive destMember, ResolutionContext context)
        => GetDealMotive(source.DealMotiveId, context.Items[DealMotivesItemsKey] as List<DealMotive>, this.asiDealMotiveIdToDealMotiveName);

    private static DealMotive? GetDealMotive(int? dealMotiveId, List<DealMotive> dealMotives, Dictionary<int, string> dealMotiveIdToDealMotiveName)
    {
        if (!dealMotiveId.HasValue)
        {
            return null;
        }

        var dealMotiveName = dealMotiveIdToDealMotiveName.TryGetValue(dealMotiveId.Value, out var outDealMotiveName)
            ? outDealMotiveName
            : null;

        var dealMotive = dealMotives
            .FirstOrDefault(x => x.Name == dealMotiveName);

        return dealMotive;
    }
}