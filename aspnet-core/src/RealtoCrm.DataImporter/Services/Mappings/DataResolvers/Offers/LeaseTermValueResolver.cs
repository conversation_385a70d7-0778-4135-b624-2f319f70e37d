namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using Nomenclatures;
using RealtoCrm.Estates;
using RealtoCrm.Offers;
using static CosherConsts.LeaseTerms;

public class LeaseTermValueResolver : IValueResolver<OfferUesImportModel, OfferDetail, LeaseTerm?>
{
    public const string LeaseTermsItemsKey = nameof(LeaseTermValueResolver);

    private static Dictionary<int, string> UesRentalTermIdToLeaseTermName =>
        new()
        {
            { 1, ShortTermName },
            { 2, ShortTermName },
            { 3, LongTermName },
        };

    public LeaseTerm? Resolve(OfferUesImportModel source, OfferDetail destination, LeaseTerm? destMember,
        ResolutionContext context)
    {
        var leaseTerms = context.Items[LeaseTermsItemsKey] as List<LeaseTerm>;

        if (source.RentalTermId == null ||
            !UesRentalTermIdToLeaseTermName.TryGetValue(source.RentalTermId.Value, out var purposeName))
        {
            return null;
        }

        return leaseTerms.FirstOrDefault(x => x.Name == purposeName);
    }
}