using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Deals;
using RealtoCrm.Offers;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;

public class OfferClientIdByOfferMappingValueResolver :
    IValueResolver<ViewingsUesImportModel, Viewing, int>,
    IValueResolver<DealsUesImporterModel, Deal, int?>
{
    public const string OffersByOfferMappingsItemsKey = nameof(OfferClientIdByOfferMappingValueResolver);
    public const string OffersItemsKey = "ViewingOffersItemsKey";


    public int Resolve(ViewingsUesImportModel source, Viewing destination, int destMember, ResolutionContext context)
        => GetOfferClientId(source.OfferId, context.Items[OffersByOfferMappingsItemsKey] as List<OfferMapping>, context.Items[OffersItemsKey] as List<Offer>);

    public int? Resolve(DealsUesImporterModel source, Deal destination, int? destMember, ResolutionContext context)
        => GetOfferClientId(source.OfferId, context.Items[OffersByOfferMappingsItemsKey] as List<OfferMapping>, context.Items[OffersItemsKey] as List<Offer>);

    private static int GetOfferClientId(int offerId, List<OfferMapping> offerMappings, List<Offer> offers)
    {
        var offerMapping = offerMappings.FirstOrDefault(om => om.AdminId == offerId);

        if (offerMapping == null)
        {
            return 0;
        }
        
        var offer = offers.FirstOrDefault(x => x.Id == offerMapping.OfferId);

        return offer is not null ? offer?.ClientId ?? 0 : 0;
    }
}