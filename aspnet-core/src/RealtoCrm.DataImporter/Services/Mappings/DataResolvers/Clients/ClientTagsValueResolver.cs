namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Clients;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using RealtoCrm.Clients;
using Tags;
using static CosherConsts.TagCategories;

public class ClientTagsValueResolver : IValueResolver<ClientsUesImportModel, Client, List<ClientTags>>
{
    public const string TagCategoriesItemKey = nameof(ClientTagsValueResolver);
    public const string ExistingTagsItemKey = "ExistingTags";

    public List<ClientTags> Resolve(ClientsUesImportModel source, Client destination,
        List<ClientTags> destMember, ResolutionContext context)
    {
        var tagCategories = context.Items[TagCategoriesItemKey] as List<TagCategory>;
        var existingTags = context.Items[ExistingTagsItemKey] as List<Tag>;

        var religionTagCategoryId = tagCategories.FirstOrDefault(c => c.Name.ToLower() == Religion.ToLower()).Id;
        var interestsTagCategoryId = tagCategories.FirstOrDefault(c => c.Name.ToLower() == Interests.ToLower()).Id;

        var tags = new List<ClientTags>();

        if (!string.IsNullOrEmpty(source.Religion))
        {
            tags.AddRange(CreateClientTagsFromString(source.Religion, religionTagCategoryId, existingTags));
        }

        if (!string.IsNullOrEmpty(source.Interest))
        {
            tags.AddRange(CreateClientTagsFromString(source.Interest, interestsTagCategoryId, existingTags));
        }

        return GetUniqueTags(tags);
    }

    private static List<ClientTags> GetUniqueTags(List<ClientTags> tags)
    {
        var existing = tags
            .Where(t => t.TagId != 0)
            .GroupBy(t => t.TagId)
            .Select(g => g.First());

        var newTags = tags
            .Where(t => t.TagId == 0);

        var uniqueTags = existing
            .Concat(newTags)
            .ToList();

        return uniqueTags;
    }

    private static List<ClientTags> CreateClientTagsFromString(string input, int categoryId, List<Tag> existingTags)
    {
        var existingTagsDict = existingTags
            .Where(t => t.CategoryId == categoryId && !string.IsNullOrWhiteSpace(t.Name))
            .GroupBy(t => t.Name.Trim().ToLowerInvariant(), StringComparer.OrdinalIgnoreCase)
            .ToDictionary(
                g => g.Key,
                g => g.First(),
                StringComparer.OrdinalIgnoreCase);

        return input
            .Split(' ', StringSplitOptions.RemoveEmptyEntries)
            .Select(word => new string(word
                .Where(char.IsLetterOrDigit)
                .ToArray()))
            .Where(cleaned => !string.IsNullOrWhiteSpace(cleaned) && cleaned.Length > 1)
            .Select(cleaned =>
            {
                var normalizedCleaned = cleaned.Trim().ToLowerInvariant();

                if (existingTagsDict.TryGetValue(normalizedCleaned, out var existing))
                {
                    return new ClientTags { 
                        TagId = existing.Id,
                        Tag = existing 
                    };
                }

                return new ClientTags
                {
                    Tag = new Tag
                    {
                        Name = cleaned,
                        CategoryId = categoryId
                    }
                };
            })
            .ToList()!;
    }
}