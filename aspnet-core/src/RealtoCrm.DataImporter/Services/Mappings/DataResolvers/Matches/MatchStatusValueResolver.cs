using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Matches;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.CosherConsts.MatchStatuses;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Matches;

public class MatchStatusValueResolver : IValueResolver<MatchesUesImportModel, Match, MatchStatus>
{
    public const string MatchStatusesItemsKey = nameof(MatchStatusValueResolver);

    private Dictionary<int, string> UesMatchStatusIdToMatchStatusName = new()
    {
        { 1, InternallyPresentedMatchStatusName },
        { 2, PresentedMatchStatusName },
        { 3, ForViewingMatchStatusName },
        { 4, ViewedMatchStatusName },
        { 5, DepositedMatchStatusName },
        { 6, PreliminaryMatchStatusName },
        { 7, ConfessionMatchStatusName },
        { 8, DeniedMatchStatusName },
        { 9, PreliminaryMatchStatusName },
    };
    
    public MatchStatus InvalidMatchStatus = new()
    {
        Id = -1,
    };

    public MatchStatus Resolve(MatchesUesImportModel source, Match destination, MatchStatus destMember, ResolutionContext context)
    {
        var matchStatuses = context.Items[MatchStatusesItemsKey] as List<MatchStatus>;
        if (source.MatchStatusId is null or 0)
        {
            return this.InvalidMatchStatus;
        }

       var matchStatusName = this.UesMatchStatusIdToMatchStatusName.First(x => x.Key == source.MatchStatusId).Value;

        return matchStatuses.First(x => x.Name == matchStatusName);
    }
}