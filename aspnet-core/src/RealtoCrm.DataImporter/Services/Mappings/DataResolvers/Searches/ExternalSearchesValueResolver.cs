using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Deals;
using RealtoCrm.ExternalSearches;
using RealtoCrm.Offers;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Searches;

public class ExternalSearchesValueResolver : IValueResolver<DealsUesImporterModel, Deal, ExternalSearch?>
{
    public const string ExternalSearchesMappingsItemsKey = nameof(ExternalSearchesValueResolver);

    public ExternalSearch? Resolve(DealsUesImporterModel source, Deal destination, ExternalSearch? destMember,
        ResolutionContext context)
    {
       if (source.SearchId == null)
       {
           return null;
       }

       var externalSearches = context.Items[ExternalSearchesMappingsItemsKey] as List<ExternalSearchesMapping>;
       return externalSearches.FirstOrDefault(x => x.AdminId == source.SearchId.Value)?.ExternalSearch;
    }
}