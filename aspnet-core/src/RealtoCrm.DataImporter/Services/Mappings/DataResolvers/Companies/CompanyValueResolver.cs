namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Companies;

using AutoMapper;
using RealtoCrm.Companies;

public class CompanyValueResolver<TSource, TDest> : IValueResolver<TSource, TDest, Company>
{
    public const string CompanyItemsKey = nameof(CompanyValueResolver<TSource, TDest>);

    public Company Resolve(TSource source, TDest destination, Company destMember, ResolutionContext context) =>
        context.Items[CompanyItemsKey] as Company;
}