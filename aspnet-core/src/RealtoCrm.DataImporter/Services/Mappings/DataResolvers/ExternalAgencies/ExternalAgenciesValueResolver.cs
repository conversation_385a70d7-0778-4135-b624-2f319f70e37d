using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.ExternalSearches;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.ExternalAgencies;

public class ExternalAgenciesValueResolver : IValueResolver<ExternalSearchUesImportModel, ExternalSearch, int>
{
    public const string ExternalAgenciesItemsKey = nameof(ExternalAgenciesValueResolver);

    public int Resolve(ExternalSearchUesImportModel source, ExternalSearch destination, int destMember,
        ResolutionContext context)
    {
        var externalAgenciesMapping = context.Items[ExternalAgenciesItemsKey] as List<ExternalAgencyMapping>;

        if (source.ExternalAgencyId is not (not null or 0)) return 0;
        var mappedExternalAgencyId = externalAgenciesMapping?.FirstOrDefault(x
            => x.AdminIds?.Contains(source.ExternalAgencyId.ToString() ?? string.Empty) ?? false)?.ExternalAgencyId;

        return mappedExternalAgencyId ?? 0;
    }
}