namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Estates;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Estates;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.CosherConsts.FacingDirections;

public class FacingDirectionValueResolver : IValueResolver<OfferUesImportModel, Estate, List<FacingDirection>>
{
    public const string FacingDirectionsItemsKey = nameof(FacingDirectionValueResolver);

    private Dictionary<int, string> UesFaceingDirectionIdsToFaceingDirectionsNames = new()
    {
        { 1, EastFacingDirectionName },
        { 2, WestFacingDirectionName },
        { 3, NorthFacingDirectionName },
        { 4, SouthFacingDirectionName },
    };

    public List<FacingDirection> Resolve(OfferUesImportModel source, Estate destination,
        List<FacingDirection> destMember, ResolutionContext context)
    {
        var facingDirections = context.Items[FacingDirectionsItemsKey] as List<FacingDirection>;

        if (!source.ExposureIds.Any())
        {
            return new List<FacingDirection>();
        }

        var matchedNames = source.ExposureIds
            .Distinct()
            .Where(id => UesFaceingDirectionIdsToFaceingDirectionsNames.ContainsKey(id))
            .Select(id => UesFaceingDirectionIdsToFaceingDirectionsNames[id])
            .ToList();
        
        return facingDirections
                .Where(fd => matchedNames.Contains(fd.Name))
                .ToList();
    }
}