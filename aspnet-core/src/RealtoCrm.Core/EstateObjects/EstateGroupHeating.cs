namespace RealtoCrm.EstateObjects;

using System;
using Abp.Domain.Entities.Auditing;
using Nomenclatures;

public class EstateGroupHeating : IFullAudited
{
    public int EstateGroupId { get; set; }

    public EstateGroup EstateGroup { get; set; } = default!;

    public int HeatingId { get; set; } = default!;

    public Heating Heating { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}