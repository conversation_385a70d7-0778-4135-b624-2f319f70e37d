using System;
using Abp.Domain.Entities.Auditing;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.Searches;

public class SearchConstructionType : IFullAudited
{
    public int SearchId { get; set; }

    public Search Search { get; set; } = default!;

    public int ConstructionTypeId { get; set; }

    public ConstructionType ConstructionType { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}