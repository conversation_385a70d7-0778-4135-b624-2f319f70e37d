namespace RealtoCrm.Searches;

using System;
using Abp.Domain.Entities.Auditing;
using Nomenclatures;

public class SearchFinancing : IFullAudited
{
    public int SearchId { get; set; }

    public Search Search { get; set; } = default!;

    public int FinancingId { get; set; }

    public Financing Financing { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}