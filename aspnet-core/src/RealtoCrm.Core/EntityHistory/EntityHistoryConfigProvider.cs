using System.Collections.Generic;
using System.Linq;
using Abp.Configuration;
using Abp.Configuration.Startup;

namespace RealtoCrm.EntityHistory;

public class EntityHistoryConfigProvider(IAbpStartupConfiguration abpStartupConfiguration) : ICustomConfigProvider
{
    public Dictionary<string, object> GetConfig(CustomConfigProviderContext customConfigProviderContext)
    {
        if (!abpStartupConfiguration.EntityHistory.IsEnabled)
        {
            return new Dictionary<string, object>
            {
                {
                    EntityHistoryHelper.EntityHistoryConfigurationName,
                    new EntityHistoryUiSetting{
                        IsEnabled = false
                    }
                }
            };
        }

        var entityHistoryEnabledEntities = new List<string>();

        foreach (var type in EntityHistoryHelper.TrackedTypes)
        {
            if (abpStartupConfiguration.EntityHistory.Selectors.Any(s => s.Predicate(type)))
            {
                entityHistoryEnabledEntities.Add(type.FullName);
            }
        }

        return new Dictionary<string, object>
        {
            {
                EntityHistoryHelper.EntityHistoryConfigurationName,
                new EntityHistoryUiSetting {
                    IsEnabled = true,
                    EnabledEntities = entityHistoryEnabledEntities
                }
            }
        };
    }
}