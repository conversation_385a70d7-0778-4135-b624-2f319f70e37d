namespace RealtoCrm.Files;

using Abp.Domain.Entities.Auditing;
using System.Collections.Generic;
using Projects;
using Estates;
using MultiTenancy.Interfaces;
using Offers;
using Viewings;
using MultiTenancy;
using Clients;

public class File : FullAuditedEntity<int>, IHaveTenant
{
    public string FileName { get; set; } = default!;
    
    public string? Title { get; set; }

    public string Source { get; set; } = default!;

    public double Size { get; set; }

    public int CategoryId { get; set; }

    public FileCategory Category { get; set; } = default!;
    
    public int TenantId { get; set; }
    
    public Tenant Tenant { get; set; } = default!;

    public int? ClientId { get; set; }
    
    public Client? Client { get; set; } = default!;

    public ICollection<OfferFile> OffersFiles { get; } = new List<OfferFile>();

    public ICollection<EstateFile> EstatesFiles { get; } = new List<EstateFile>();

    public ICollection<ProjectFile> ProjectsFiles { get; } = new List<ProjectFile>();

    public ICollection<ViewingFile> ViewingsFiles { get; } = new List<ViewingFile>();
    
    public ICollection<OfferEncumbranceFile> OfferEncumbranceFiles { get; } = new List<OfferEncumbranceFile>();
}