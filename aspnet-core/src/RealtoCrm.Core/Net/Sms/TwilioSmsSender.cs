using System.Threading.Tasks;
using Abp.Dependency;
using RealtoCrm.Identity;
using Twilio;
using Twilio.Rest.Api.V2010.Account;

namespace RealtoCrm.Net.Sms;

public class TwilioSmsSender(TwilioSmsSenderConfiguration twilioSmsSenderConfiguration) : ISmsSender, ITransientDependency
{
    public async Task SendAsync(string number, string message)
    {
        TwilioClient.Init(twilioSmsSenderConfiguration.AccountSid, twilioSmsSenderConfiguration.AuthToken);

        var resource = await MessageResource.CreateAsync(
            body: message,
            @from: new Twilio.Types.PhoneNumber(twilioSmsSenderConfiguration.SenderNumber),
            to: new Twilio.Types.PhoneNumber(number)
        );
    }
}