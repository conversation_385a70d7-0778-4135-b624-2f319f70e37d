namespace RealtoCrm.Companies;

using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Employees;
using MultiTenancy;
using MultiTenancy.Interfaces;

public class Team : FullAuditedEntity<int>, IHaveTenant
{
    public string Name { get; set; } = default!;

    public bool IsActive { get; set; }

    public int? ParentId { get; set; }

    public Team? Parent { get; set; }

    public int TenantId { get; set; }

    public Tenant Tenant { get; set; } = default!;

    public int CompanyId { get; set; }

    public Company Company { get; set; } = default!;

    public int? DepartmentId { get; set; }

    public Department? Department { get; set; }
    
    public ICollection<Team> Children { get; } = new List<Team>();

    public ICollection<Employee> Employees { get; } = new List<Employee>();
}