using System;
using Abp.AspNetZeroCore;
using Abp.AspNetZeroCore.Timing;
using Abp.AutoMapper;
using Abp.Dependency;
using Abp.Modules;
using Abp.Net.Mail;
using Abp.Reflection.Extensions;
using Abp.Timing;
using Abp.Configuration.Startup;
using Abp.Net.Mail.Smtp;
using Abp.Zero;
using Abp.Zero.Ldap;
using Castle.MicroKernel.Registration;
using MailKit.Security;
using RealtoCrm.Authorization.Delegation;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Configuration;
using RealtoCrm.DashboardCustomization.Definitions;
using RealtoCrm.Debugging;
using RealtoCrm.DynamicEntityProperties;
using RealtoCrm.Features;
using RealtoCrm.Localization;
using RealtoCrm.MultiTenancy;
using RealtoCrm.Net.Emailing;
using RealtoCrm.Notifications;

namespace RealtoCrm;

using Abp.MailKit;
using Abp.Zero.Configuration;
using Webhooks;

[DependsOn(
    typeof(RealtoCrmCoreSharedModule),
    typeof(AbpZeroCoreModule),
    typeof(AbpZeroLdapModule),
    typeof(AbpAutoMapperModule),
    typeof(AbpAspNetZeroCoreModule),
    typeof(AbpMailKitModule))]
public class RealtoCrmCoreModule : AbpModule
{
    public override void PreInitialize()
    {
        //workaround for issue: https://github.com/aspnet/EntityFrameworkCore/issues/9825
        //related github issue: https://github.com/aspnet/EntityFrameworkCore/issues/10407
        AppContext.SetSwitch("Microsoft.EntityFrameworkCore.Issue9825", true);

        this.Configuration.Auditing.IsEnabledForAnonymousUsers = true;

        //Declare entity types
        this.Configuration.Modules.Zero().EntityTypes.Tenant = typeof(Tenant);
        this.Configuration.Modules.Zero().EntityTypes.Role = typeof(Role);
        this.Configuration.Modules.Zero().EntityTypes.User = typeof(User);

        RealtoCrmLocalizationConfigurer.Configure(this.Configuration.Localization);

        //Adding feature providers
        this.Configuration.Features.Providers.Add<AppFeatureProvider>();

        //Adding setting providers
        this.Configuration.Settings.Providers.Add<AppSettingProvider>();

        //Adding notification providers
        this.Configuration.Notifications.Providers.Add<AppNotificationProvider>();

        //Adding webhook definition providers
        this.Configuration.Webhooks.Providers.Add<AppWebhookDefinitionProvider>();
        this.Configuration.Webhooks.TimeoutDuration = TimeSpan.FromMinutes(1);
        this.Configuration.Webhooks.IsAutomaticSubscriptionDeactivationEnabled = false;

        //Enable this line to create a multi-tenant application.
        this.Configuration.MultiTenancy.IsEnabled = RealtoCrmConsts.MultiTenancyEnabled;

        //Enable LDAP authentication 
        //this.Configuration.Modules.ZeroLdap().Enable(typeof(AppLdapAuthenticationSource));

        //Twilio - Enable this line to activate Twilio SMS integration
        //this.Configuration.ReplaceService<ISmsSender,TwilioSmsSender>();

        //Adding DynamicEntityParameters definition providers
        this.Configuration.DynamicEntityProperties.Providers.Add<AppDynamicEntityPropertyDefinitionProvider>();

        // MailKit configuration
        this.Configuration.Modules.AbpMailKit().SecureSocketOption = SecureSocketOptions.Auto;
        this.Configuration.ReplaceService<IMailKitSmtpBuilder, RealtoCrmMailKitSmtpBuilder>(DependencyLifeStyle.Transient);

        //Configure roles
        AppRoleConfig.Configure(this.Configuration.Modules.Zero().RoleManagement);

        if (DebugHelper.IsDebug)
        {
            //Disabling email sending in debug mode
            this.Configuration.ReplaceService<IEmailSender, NullEmailSender>(DependencyLifeStyle.Transient);
        }

        this.Configuration.ReplaceService(typeof(IEmailSenderConfiguration), () =>
        {
            this.Configuration.IocManager.IocContainer.Register(
                Component.For<IEmailSenderConfiguration, ISmtpEmailSenderConfiguration>()
                    .ImplementedBy<RealtoCrmSmtpEmailSenderConfiguration>()
                    .LifestyleTransient()
            );
        });

        this.IocManager.Register<DashboardConfiguration>();
            
        this.Configuration.Notifications.Notifiers.Add<SmsRealTimeNotifier>();
        this.Configuration.Notifications.Notifiers.Add<EmailRealTimeNotifier>();

    }

    public override void Initialize()
    {
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmCoreModule).GetAssembly());
    }

    public override void PostInitialize()
    {
        this.IocManager.Register<IUserDelegationConfiguration, UserDelegationConfiguration>();

        this.IocManager.Resolve<AppTimes>().StartupTime = Clock.Now;
    }
}