namespace RealtoCrm.Projects;

using System;
using Abp.Domain.Entities.Auditing;
using Images;

public class ProjectImage : IFullAudited
{
    public int ProjectId { get; set; }

    public Project Project { get; set; } = default!;

    public int ImageId { get; set; }

    public Image Image { get; set; } = default!;

    public bool IsActive { get; set; }

    public bool IsFeatured { get; set; }

    public int Order { get; set; }

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}