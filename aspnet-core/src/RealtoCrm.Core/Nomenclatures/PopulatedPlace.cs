namespace RealtoCrm.Nomenclatures;

using System.Collections.Generic;
using Addresses;
using Searches;
using Translations;

public class PopulatedPlace : Nomenclature<int>
{
    public string Code { get; set; } = default!;

    public string Ekatte { get; set; } = default!;

    public PopulatedPlaceType Type { get; set; }

    public double Latitude { get; set; }

    public double Longitude { get; set; }

    public int MunicipalityId { get; set; }

    public Municipality Municipality { get; set; } = default!;

    public ICollection<Street> Streets { get; } = new List<Street>();

    public ICollection<District> Districts { get; } = new List<District>();

    public ICollection<Address> Addresses { get; } = new List<Address>();

    public ICollection<SearchPopulatedPlace> SearchesPopulatedPlaces { get; } = new List<SearchPopulatedPlace>();
    
    public ICollection<PopulatedPlaceTranslation> Translations { get; } = new List<PopulatedPlaceTranslation>();
}