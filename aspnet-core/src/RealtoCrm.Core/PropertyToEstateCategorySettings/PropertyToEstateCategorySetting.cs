namespace RealtoCrm.PropertyToEstateCategorySettings;

using RealtoCrm.Estates;
using RealtoCrm.MultiTenancy;
using Abp.Domain.Entities.Auditing;

public class PropertyToEstateCategorySetting : FullAuditedEntity<int>
{
    public string PropertyName { get; set; } = default!;

    public PropertyToEstateCategoryModel Model { get; set; }

    public int CategoryId { get; set; } = default!;
    
    public EstateCategory Category { get; set; } = default!;

    public bool IsVisible { get; set; }
    
    public int TenantId { get; set; }

    public Tenant Tenant { get; set; } = default!;
    
    public RequiredSettingForOfferStatus RequiredForOfferStatus { get; set; }
}