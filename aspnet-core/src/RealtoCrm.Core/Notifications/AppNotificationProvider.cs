using Abp.Authorization;
using Abp.Localization;
using Abp.Notifications;
using RealtoCrm.Authorization;

namespace RealtoCrm.Notifications;

public class AppNotificationProvider : NotificationProvider
{
    public override void SetNotifications(INotificationDefinitionContext context)
    {
        context.Manager.Add(
            new NotificationDefinition(
                AppNotificationNames.NewUserRegistered,
                displayName: L("NewUserRegisteredNotificationDefinition")
            )
        );

        context.Manager.Add(
            new NotificationDefinition(
                AppNotificationNames.NewTenantRegistered,
                displayName: L("NewTenantRegisteredNotificationDefinition")
            )
        );
    }

    private static ILocalizableString L(string name)
    {
        return new LocalizableString(name, RealtoCrmConsts.LocalizationSourceName);
    }
}