<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <AssemblyName>RealtoCrm.Core</AssemblyName>
    <PackageId>RealtoCrm.Core</PackageId>
    <GenerateAssemblyTitleAttribute>false</GenerateAssemblyTitleAttribute>
    <GenerateAssemblyDescriptionAttribute>false</GenerateAssemblyDescriptionAttribute>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <UserSecretsId>MyCompanyName-RealtoCrm-894FDFC1-6482-4A56-926A-3C46C9FE0329</UserSecretsId>
    <RootNamespace>RealtoCrm</RootNamespace>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <EmbeddedResource Include="Localization\RealtoCrm\*.xml;Net\Emailing\EmailTemplates\default.html" Exclude="bin\**;obj\**;**\*.xproj;packages\**;@(EmbeddedResource)" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Localization\RealtoCrm\RealtoCrm-bg-BG.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="8.0.0" />
    <PackageReference Include="Castle.Windsor.MsDependencyInjection" Version="4.1.0" />
    <PackageReference Include="PayPalHttp" Version="1.0.1" />
    <PackageReference Include="PayPalCheckoutSdk" Version="1.0.4" />
    <PackageReference Include="Stripe.net" Version="43.3.0" />
    <PackageReference Include="TimeZoneConverter" Version="6.1.0" />
    <PackageReference Include="Abp.AspNetZeroCore" Version="4.1.0" />
    <PackageReference Include="SkiaSharp" Version="2.88.6" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Abp.ZeroCore.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Abp.AutoMapper" Version="9.0.0" />
    <PackageReference Include="Abp.MailKit" Version="9.0.0" />
    <PackageReference Include="Abp.Zero.Ldap" Version="9.0.0" />
    <PackageReference Include="Twilio" Version="6.15.1" />
    <PackageReference Include="OpenIddict.Core" Version="4.10.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RealtoCrm.Core.Shared\RealtoCrm.Core.Shared.csproj" />
  </ItemGroup>
</Project>