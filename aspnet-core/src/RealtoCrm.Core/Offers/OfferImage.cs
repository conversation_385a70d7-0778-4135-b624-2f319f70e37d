namespace RealtoCrm.Offers;

using System;
using Abp.Domain.Entities.Auditing;
using Images;

public class OfferImage : IFullAudited
{
    public int OfferId { get; set; }

    public Offer Offer { get; set; } = default!;

    public int ImageId { get; set; }

    public Image Image { get; set; } = default!;

    public DateTime CreationTime { get; set; }

    public long? CreatorUserId { get; set; }
    
    public bool IsActive { get; set; }
    
    public bool IsFeatured { get; set; }
    
    public int Order { get; set; }

    public DateTime? LastModificationTime { get; set; }

    public long? LastModifierUserId { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? DeletionTime { get; set; }

    public long? DeleterUserId { get; set; }
}