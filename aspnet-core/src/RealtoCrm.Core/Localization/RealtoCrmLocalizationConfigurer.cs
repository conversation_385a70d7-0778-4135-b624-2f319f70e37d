using System.Reflection;
using Abp.Configuration.Startup;
using Abp.Localization.Dictionaries;
using Abp.Localization.Dictionaries.Xml;
using Abp.Reflection.Extensions;

namespace RealtoCrm.Localization;

public static class RealtoCrmLocalizationConfigurer
{
    public static void Configure(ILocalizationConfiguration localizationConfiguration)
    {
        localizationConfiguration.Sources.Add(
            new DictionaryBasedLocalizationSource(
                RealtoCrmConsts.LocalizationSourceName,
                new XmlEmbeddedFileLocalizationDictionaryProvider(
                    typeof(RealtoCrmLocalizationConfigurer).GetAssembly(),
                    "RealtoCrm.Localization.RealtoCrm"
                )
            )
        );
    }
}