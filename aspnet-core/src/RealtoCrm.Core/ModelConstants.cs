namespace RealtoCrm;

public static class ModelConstants
{
    public static class Common
    {
        public const int MinNameLength = 1;
        public const int MaxNameLength = 50;
        public const int MinEmailLength = 3;
        public const int MaxEmailLength = 320;
        public const int MinPhoneNumberLength = 5;
        public const int MaxPhoneNumberLength = 15;
        public const string PhoneNumberRegularExpression = @"^(\+359|0)([2-9][0-9]{7,8})$";
        public const int MinDescriptionLength = 3;
        public const int MaxDescriptionLength = 200;
        public const int MinIdentificationNumberLength = 2;
        public const int MaxIdentificationNumberLength = 30;
        public const int MaxUrlLength = 2048;
        public const int MinNomenclatureTypeLength = 1;
        public const int MaxNomenclatureTypeLength = 255;
    }

    public static class UserProfile
    {
        public const int MinDisplayNameLength = 2;
        public const int MaxDisplayNameLength = 150;
        public const int MinSimCardNumberLength = 2;
        public const int MaxSimCardNumberLength = 22;
        public const int MinWorkPositionLength = 2;
        public const int MaxWorkPositionLength = 100;
    }

    public static class Offer
    {
        public const int MaxOfferNameLength = 200;
        public const int MaxOfferDescriptionLength = 5000;
    }

    public static class Project
    {
        public const int MaxProjectTitleLength = 256;
    }
}