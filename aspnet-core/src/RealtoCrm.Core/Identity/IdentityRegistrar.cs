using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using RealtoCrm.Authentication.TwoFactor.Google;
using RealtoCrm.Authorization;
using RealtoCrm.Authorization.Roles;
using RealtoCrm.Authorization.Users;
using RealtoCrm.Editions;
using RealtoCrm.MultiTenancy;

namespace RealtoCrm.Identity;

public static class IdentityRegistrar
{
    public static IdentityBuilder AddIdentity(this IServiceCollection services)
    {
        services.AddLogging();

        return services.AddAbpIdentity<Tenant, User, Role>(options =>
            {
                options.Tokens.ProviderMap[GoogleAuthenticatorProvider.Name] = new TokenProviderDescriptor(typeof(GoogleAuthenticatorProvider));
            })
            .AddAbpTenantManager<TenantManager>()
            .AddAbpUserManager<UserManager>()
            .AddAbpRoleManager<RoleManager>()
            .AddAbpEditionManager<EditionManager>()
            .AddAbpUserStore<UserStore>()
            .AddAbpRoleStore<RoleStore>()
            .AddAbpSignInManager<SignInManager>()
            .AddAbpUserClaimsPrincipalFactory<UserClaimsPrincipalFactory>()
            .AddAbpSecurityStampValidator<SecurityStampValidator>()
            .AddPermissionChecker<PermissionChecker>()
            .AddDefaultTokenProviders();
    }
}