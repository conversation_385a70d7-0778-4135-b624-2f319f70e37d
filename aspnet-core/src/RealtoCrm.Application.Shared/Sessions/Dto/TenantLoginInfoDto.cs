using System;
using System.Collections.Generic;
using Abp.Application.Services.Dto;
using Abp.Timing;
using RealtoCrm.MultiTenancy.Payments;
using RealtoCrm.Mapping;
using RealtoCrm.MultiTenancy;

namespace RealtoCrm.Sessions.Dto;

public class TenantLoginInfoDto
    : EntityDto,
        IMapFrom<Tenant>
{
    public string TenancyName { get; set; }

    public string Name { get; set; }

    public virtual Guid? DarkLogoId { get; set; }

    public virtual string DarkLogoFileType { get; set; }

    public virtual Guid? DarkLogoMinimalId { get; set; }

    public virtual string DarkLogoMinimalFileType { get; set; }

    public virtual Guid? LightLogoId { get; set; }

    public virtual string LightLogoFileType { get; set; }

    public virtual Guid? LightLogoMinimalId { get; set; }

    public virtual string LightLogoMinimalFileType { get; set; }

    public Guid? CustomCssId { get; set; }

    public DateTime? SubscriptionEndDateUtc { get; set; }

    public bool IsInTrialPeriod { get; set; }

    public SubscriptionPaymentType SubscriptionPaymentType { get; set; }

    public EditionInfoDto Edition { get; set; }

    public List<NameValueDto> FeatureValues { get; set; } = new List<NameValueDto>();

    public DateTime CreationTime { get; set; }

    public PaymentPeriodType PaymentPeriodType { get; set; }

    public string SubscriptionDateString { get; set; }

    public string CreationTimeString { get; set; }

    public bool IsInTrial()
    {
        return this.IsInTrialPeriod;
    }

    public bool SubscriptionIsExpiringSoon(int subscriptionExpireNootifyDayCount)
    {
        if (this.SubscriptionEndDateUtc.HasValue)
        {
            return Clock.Now.ToUniversalTime().AddDays(subscriptionExpireNootifyDayCount) >= this.SubscriptionEndDateUtc.Value;
        }

        return false;
    }

    public int GetSubscriptionExpiringDayCount()
    {
        if (!this.SubscriptionEndDateUtc.HasValue)
        {
            return 0;
        }

        return Convert.ToInt32(this.SubscriptionEndDateUtc.Value.ToUniversalTime().Subtract(Clock.Now.ToUniversalTime()).TotalDays);
    }

    public bool HasRecurringSubscription()
    {
        return this.SubscriptionPaymentType != SubscriptionPaymentType.Manual;
    }

    public virtual bool HasLogo()
    {
        return (this.DarkLogoId != null && this.DarkLogoFileType != null) ||
               (this.LightLogoId != null && this.LightLogoFileType != null);
    }
}