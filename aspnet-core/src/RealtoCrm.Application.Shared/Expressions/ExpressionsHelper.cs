namespace RealtoCrm.Expressions;

using System;
using System.Linq;
using System.Linq.Expressions;

public static class ExpressionsHelper
{
    public static Expression<Func<T, bool>> And<T>(params Expression<Func<T, bool>>[] expressions)
     => expressions.Aggregate((left, right) => left.AndAlso(right));

    public static Expression<Func<T, bool>> Or<T>(params Expression<Func<T, bool>>[] expressions)
     => expressions.Aggregate((left, right) => left.OrElse(right));

    public static Expression<Func<T, bool>> Not<T>(Expression<Func<T, bool>> expression)
    {
        var param = expression.Parameters[0];
        var body = Expression.Not(expression.Body);
        return Expression.Lambda<Func<T, bool>>(body, param);
    }

    public static Expression<Func<T, bool>> AndAlso<T>(this Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
    {
        var param = left.Parameters[0];
        var body = Expression.AndAlso(left.Body, Expression.Invoke(right, param));
        return Expression.Lambda<Func<T, bool>>(body, param);
    }

    public static Expression<Func<T, bool>> OrElse<T>(this Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
    {
        var param = left.Parameters[0];
        var body = Expression.OrElse(left.Body, Expression.Invoke(right, param));
        return Expression.Lambda<Func<T, bool>>(body, param);
    }
}