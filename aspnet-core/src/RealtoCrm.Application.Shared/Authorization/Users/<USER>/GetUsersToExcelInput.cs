using System.Collections.Generic;
using Abp.Runtime.Validation;

namespace RealtoCrm.Authorization.Users.Dto;

public class GetUsersToExcelInput : IShouldNormalize, IGetUsersInput
{
    public string? Filter { get; set; }

    public List<string> Permissions { get; set; } = new();

    public int? Role { get; set; }

    public bool OnlyLockedUsers { get; set; }

    public string? Sorting { get; set; }

    public void Normalize()
    {
        if (string.IsNullOrEmpty(this.Sorting))
        {
            this.Sorting = "Name,Surname";
        }
    }
}