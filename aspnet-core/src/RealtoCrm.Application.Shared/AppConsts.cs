using System;

namespace RealtoCrm;

/// <summary>
/// Some consts used in the application.
/// </summary>
public static class AppConsts
{
    /// <summary>
    /// Default page size for paged requests.
    /// </summary>
    public const int DefaultPageSize = 10;

    /// <summary>
    /// Maximum allowed page size for paged requests.
    /// </summary>
    public const int MaxPageSize = 1000;

    /// <summary>
    /// Default initial page for paged requests.
    /// </summary>
    public const int DefaultPage = 0;

    /// <summary>
    /// Default pass phrase for SimpleStringCipher decrypt/encrypt operations
    /// </summary>
    public const string DefaultPassPhrase = "8737ae33f6d844f4a79605b4dea2d37b";

    public const int ResizedMaxProfilePictureBytesUserFriendlyValue = 1024;

    public const int MaxProfilePictureBytesUserFriendlyValue = 5;

    public const string TokenValidityKey = "token_validity_key";
    public const string RefreshTokenValidityKey = "refresh_token_validity_key";
    public const string SecurityStampKey = "AspNet.Identity.SecurityStamp";

    public const string TokenType = "token_type";

    public const string UserIdentifier = "user_identifier";

    public const string ThemeDefault = "default";
    public const string Theme2 = "theme2";
    public const string Theme3 = "theme3";
    public const string Theme4 = "theme4";
    public const string Theme5 = "theme5";
    public const string Theme6 = "theme6";
    public const string Theme7 = "theme7";
    public const string Theme8 = "theme8";
    public const string Theme9 = "theme9";
    public const string Theme10 = "theme10";
    public const string Theme11 = "theme11";
    public const string Theme12 = "theme12";
    public const string Theme13 = "theme13";

    public static readonly TimeSpan AccessTokenExpiration = TimeSpan.FromDays(1);
    public static readonly TimeSpan RefreshTokenExpiration = TimeSpan.FromDays(365);

    /// <summary>
    /// Max file import size
    /// </summary>
    public const int MaxFileSize = 1024 * 1024;
}