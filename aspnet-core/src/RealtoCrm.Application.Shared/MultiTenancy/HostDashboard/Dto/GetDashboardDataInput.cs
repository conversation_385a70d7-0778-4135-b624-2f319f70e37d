using Abp.Runtime.Validation;

namespace RealtoCrm.MultiTenancy.HostDashboard.Dto;

public class GetDashboardDataInput : DashboardInputBase, IShouldNormalize
{
    public ChartDateInterval IncomeStatisticsDateInterval { get; set; }
    public void Normalize()
    {
        this.TrimTime();
    }

    private void TrimTime()
    {
        this.StartDate = this.StartDate.Date;
        this.StartDate = this.StartDate.Date;
    }
}