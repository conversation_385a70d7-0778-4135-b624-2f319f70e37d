namespace RealtoCrm.DataCrudModels;

using System;
using System.Linq.Expressions;

public class SortByExpressionDefinition<TEntity>
{
    public Expression<Func<TEntity, object>> Expression { get; init; }

    public DataSortDirection? Direction { get; init; }

    public static SortByExpressionDefinition<TEntity> Ascending<TEntity>(Expression<Func<TEntity, object>> expression)
        => new()
        {
            Expression = expression,
            Direction = DataSortDirection.Ascending
        };

    public static SortByExpressionDefinition<TEntity> Descending<TEntity>(Expression<Func<TEntity, object>> expression)
        => new()
        {
            Expression = expression,
            Direction = DataSortDirection.Descending
        };
}