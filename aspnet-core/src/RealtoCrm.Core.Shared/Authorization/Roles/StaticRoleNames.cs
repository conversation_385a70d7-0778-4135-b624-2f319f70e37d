namespace RealtoCrm.Authorization.Roles;

public static class StaticRoleNames
{
    public static class Host
    {
        public static readonly string Admin = "Админ";
    }

    public static class Tenants
    {
        public static readonly string Admin = "Админ";

        public static readonly string User = "Потребител";
        
        public static readonly string TeamLeader = "Тийм лидер";

        public static readonly string Consultant = "Консултант";

        public static readonly string Manager = "Мениджър";

        public static readonly string Editor = "Редактор";

        public static readonly string Photograph = "Фотограф";

        public static readonly string AssistantConsultant = "Асистент консултант";

        public static readonly string AssistantManager = "Асистент мениджър";

        public static readonly string Supervisor = "Супервайзър";

        public static readonly string Unallocated = "Неразпределен";
    }
}