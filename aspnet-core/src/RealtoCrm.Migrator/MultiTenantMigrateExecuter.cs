using System;
using System.Collections.Generic;
using Abp.Data;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.MultiTenancy;
using Abp.Runtime.Security;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.MultiTenancy;

namespace RealtoCrm.Migrator;

using EntityFrameworkCore.Migrations.Seed;

public class MultiTenantMigrateExecuter(
    AbpZeroDbMigrator migrator,
    IRepository<Tenant> tenantRepository,
    Log log,
    IDbPerTenantConnectionStringResolver connectionStringResolver)
    : ITransientDependency
{
    public Log Log { get; private set; } = log;

    public void Run(bool skipConnVerification, bool isDockerEnabled = false)
    {
        var hostConnStr = connectionStringResolver.GetNameOrConnectionString(new ConnectionStringResolveArgs(MultiTenancySides.Host));
        if (hostConnStr.IsNullOrWhiteSpace())
        {
            this.Log.Write("Configuration file should contain a connection string named 'Default'");
            return;
        }

        this.Log.Write("Host database: " + ConnectionStringHelper.GetConnectionString(hostConnStr));

        if (!skipConnVerification && !isDockerEnabled)
        {
            this.Log.Write("Continue to migration for this host database and all tenants..? (Y/N): ");
            var command = Console.ReadLine();
            if (!command.IsIn("Y", "y"))
            {
                this.Log.Write("Migration canceled.");
                return;
            }
        }

        this.Log.Write("HOST database migration started...");

        try
        {
            migrator.CreateOrMigrateForHost(SeedHelper.SeedHostDb);
        }
        catch (Exception ex)
        {
            this.Log.Write("An error occured during migration of host database:");
            this.Log.Write(ex.ToString());
            this.Log.Write("Canceled migrations.");
            return;
        }

        this.Log.Write("HOST database migration completed.");
        this.Log.Write("--------------------------------------------------------");

        var migratedDatabases = new HashSet<string>();
        var tenants = tenantRepository.GetAllList(t => t.ConnectionString != null && t.ConnectionString != "");
        for (var i = 0; i < tenants.Count; i++)
        {
            var tenant = tenants[i];
            this.Log.Write(string.Format("Tenant database migration started... ({0} / {1})", (i + 1), tenants.Count));
            this.Log.Write("Name              : " + tenant.Name);
            this.Log.Write("TenancyName       : " + tenant.TenancyName);
            this.Log.Write("Tenant Id         : " + tenant.Id);
            this.Log.Write("Connection string : " + SimpleStringCipher.Instance.Decrypt(tenant.ConnectionString));

            if (!migratedDatabases.Contains(tenant.ConnectionString))
            {
                try
                {
                    migrator.CreateOrMigrateForTenant(tenant);
                }
                catch (Exception ex)
                {
                    this.Log.Write("An error occured during migration of tenant database:");
                    this.Log.Write(ex.ToString());
                    this.Log.Write("Skipped this tenant and will continue for others...");
                }

                migratedDatabases.Add(tenant.ConnectionString);
            }
            else
            {
                this.Log.Write("This database has already migrated before (you have more than one tenant in same database). Skipping it....");
            }

            this.Log.Write(string.Format("Tenant database migration completed. ({0} / {1})", (i + 1), tenants.Count));
            this.Log.Write("--------------------------------------------------------");
        }

        this.Log.Write("All databases have been migrated.");
    }
}