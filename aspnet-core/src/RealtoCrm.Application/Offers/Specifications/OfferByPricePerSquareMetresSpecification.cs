namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;

public class OfferByPricePerSquareMetresSpecification : Specification<Offer>
{
    private readonly int? pricePerSquareMetreFrom;
    private readonly int? pricePerSquareMetreTo;

    public OfferByPricePerSquareMetresSpecification(int? pricePerSquareMetreFrom, int? pricePerSquareMetreTo)
    {
        this.pricePerSquareMetreFrom = pricePerSquareMetreFrom;
        this.pricePerSquareMetreTo = pricePerSquareMetreTo;
    }

    protected override bool Include => this.pricePerSquareMetreFrom is not null ||
                                       this.pricePerSquareMetreTo is not null || this.pricePerSquareMetreFrom > 0 ||
                                       this.pricePerSquareMetreTo > 0;

    public override Expression<Func<Offer, bool>> ToExpression()
        => offer => offer.SquareMetrePrice != null && (pricePerSquareMetreFrom != null && pricePerSquareMetreTo != null
            ? offer.SquareMetrePrice.Amount >= pricePerSquareMetreFrom &&
              offer.SquareMetrePrice.Amount <= pricePerSquareMetreTo
            : pricePerSquareMetreFrom != null
                ? offer.SquareMetrePrice.Amount >= pricePerSquareMetreFrom
                : offer.SquareMetrePrice.Amount <= pricePerSquareMetreTo);
}