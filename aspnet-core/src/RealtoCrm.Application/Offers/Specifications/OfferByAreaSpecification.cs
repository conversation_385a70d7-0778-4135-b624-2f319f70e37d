namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;

public class OfferByAreaSpecification(double? areaFrom, double? areaTo) : Specification<Offer>
{
    protected override bool Include
        => areaFrom is not null || areaTo is not null || areaFrom > 0 || areaTo > 0;

    public override Expression<Func<Offer, bool>> ToExpression()
        => offer => areaFrom != null && areaTo != null
            ? offer.Estate.Area >= areaFrom && offer.Estate.Area <= areaTo
            : areaFrom != null && areaTo == null
                ? offer.Estate.Area >= areaFrom
                : offer.Estate.Area <= areaTo;
}