namespace RealtoCrm.Offers;
using RealtoCrm.CMA.Specifications;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp;
using Abp.BackgroundJobs;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Abp.UI;
using Addresses;
using Authorization;
using Calls;
using Clients;
using CommentsTasks;
using Common.Attributes;
using Common.Extensions;
using Common.Models;
using Configuration;
using Contracts;
using DataCrudModels;
using Deals;
using Deals.Models;
using Employees;
using Employees.Models;
using EntityFrameworkCore;
using Estates;
using Extensions;
using Jobs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Models;
using Expressions;
using Matches.Models;
using Microsoft.EntityFrameworkCore;
using Models.Permissions;
using Money;
using Nomenclatures;
using Nomenclatures.ImageCategories;
using Nomenclatures.OfferStatuses;
using Nomenclatures.PopulatedPlaces;
using Notifications;
using OffersEmployeesChangeHistory;
using OffersImages;
using OffersImages.Models;
using Providers;
using Searches;
using Searches.Models;
using Searches.Models.Permissions;
using Specifications;
using Storage;
using Viewings.Models;
using Tasks.Models;
using static CosherConsts.OfferStatuses;
using static CosherConsts.ImageCategories;

public class OffersAppService(
    IDateTimeService dateTimeService,
    IOffersImagesAppService offersImagesAppService,
    IBinaryObjectManager binaryObjectManager,
    IBackgroundJobManager backgroundJobManager,
    ICallsAppService callsAppService,
    IContractsAppService contractsAppService,
    IEmployeesAppService employeesAppService,
    IOfferStatusesAppService offerStatusesAppService,
    IPopulatedPlacesAppService populatedPlacesAppService,
    IImageCategoriesAppService imageCategoriesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder,
    IAppNotifier appNotifier,
    IOfferStatusesAppService offerStatusesService,
    IOfferPermissionsChecker offerPermissionsChecker)
    : CommentsTasksAppService<
        int,
        Offer,
        OfferComment,
        OfferTask,
        OfferCreateRequestModel,
        OfferUpdateRequestModel,
        OffersPaginatedRequest,
        OfferDetailsResponseModel,
        OfferListingResponseModel>(employeesAppService, dbContextProvider, expressionsBuilder), IOffersAppService
{
    private const int DealSearchStatusId = 3;
    private const int DealOfferStatusId = 5;
    private const int SellOperationTypeId = 1;
    private const int RentOperationTypeId = 4;
    private const int RegisteredOfferPastYears = 3;
    private const int PotentialOfferStatusId = 2;
    private const int ActiveOfferStatusId = 3;

    protected override Specification<Offer> GetSpecification(OffersPaginatedRequest request)
        => new OffersByClientIdSpecification(request.SearchTerm)
            .And(new OfferByIdSpecification(request.Id))
            .And(new OfferByEmployeeIdSpecification(request.EmployeeId))
            .And(new OfferByAreaSpecification(request.AreaFrom, request.AreaTo))
            .And(new OfferByPriceSpecification(request.MoneyFrom, request.MoneyTo))
            .And(new OfferByPricePerSquareMetresSpecification(request.SquareMetrePriceFrom, request.SquareMetrePriceTo))
            .And(new OfferByEstateTypeSpecification(request.EstateType))
            .And(new OfferByOperationTypeSpecification(request.OperationType))
            .And(new OfferByDepositSpecification(request.DepositId))
            .And(new OfferByAddressSpecification(request.AddressFilteringModel))
            .And(new OfferByAdvancedFiltersSpecification(request.AdvancedFilters))
            .And(new OfferByOwnershipStatusSpecification(
                request.OwnershipStatus,
                this.AbpSession.UserId,
                request.CurrentUserEmployeeId));

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.OfferCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.OfferUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.OfferUpdate)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.OfferRead)
            .ConfigureAuthorize(nameof(this.UploadImagesAsync), AppPermissions.OfferUpdatePicturesFor)
            .ConfigureAuthorize(nameof(this.ArchiveAsync), AppPermissions.OfferArchiveAction)
            .ConfigureAuthorize(nameof(this.OfferToEmployeeAsync), AppPermissions.OfferOfferToEmployeeAction)
            .ConfigureAuthorize(nameof(this.TransferOfferToEmployeeAsync), AppPermissions.OfferTransferToEmployeeAction)
            .ConfigureAuthorize(nameof(this.ChangeOwnerAsync), AppPermissions.OfferChangeOwnerAction);

    [HttpPost]
    public async Task ImportExcelAsync(IFormFile? file)
    {
        if (file is null)
        {
            throw new UserFriendlyException(this.L("File_Empty_Error"));
        }

        if (file.Length > AppConsts.MaxFileSize)
        {
            throw new UserFriendlyException(this.L("File_SizeLimit_Error"));
        }

        var fileBytes = await file.ToBytesAsync();
        var tenantId = this.AbpSession.TenantId;
        var fileObject = new BinaryObject(tenantId, fileBytes, $"{DateTime.UtcNow} import from excel file.");

        await binaryObjectManager.SaveAsync(fileObject);

        await backgroundJobManager.EnqueueAsync<ImportOffersFromExcelJob, ImportOffersFromExcelJobArgs>(
            new ImportOffersFromExcelJobArgs
            {
                TenantId = tenantId,
                BinaryObjectId = fileObject.Id,
                User = new UserIdentifier(tenantId, this.AbpSession.GetUserId())
            });
    }

    protected override async Task<IEnumerable<OfferListingResponseModel>> GetListingAsync(
        Specification<Offer> specification, Expression<Func<Offer, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<Offer>> sortByExpressions,
        int skip = 0, int take = Int32.MaxValue)
    {
        var offers = await this.ObjectMapper
            .ProjectTo<OfferListingResponseModel>(this
                .GetEntityQuery(specification, filtersExpression)
                .ApplySortByDefinitions(sortByExpressions)
                .Skip(skip)
                .Take(take))
            .ToListAsync();

        return offers.OrderBy(item => GetSortPriority(item.OfferStatusId.Value));
    }

    public async Task ActivateAsync(int offerId)
    {
        var offer = await this.GetByIdAsync(offerId);

        offer.OfferStatusId = await offerStatusesService.GetIdByNameAsync(ActiveOfferStatusName);
        offer.ArchiveReasonId = null;
        offer.ArchiveDate = null;

        await this.Data.SaveChangesAsync();
    }

    public async Task ArchiveAsync(ArchiveRequestModel request)
    {
        var isEditGranted = await offerPermissionsChecker.IsEditGrantedAsync(
            request.EntityId,
            this.AbpSession.GetUserId());

        if (!isEditGranted)
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        var offer = await this.GetByIdAsync(request.EntityId);

        offer.OfferStatusId = await offerStatusesService.GetIdByNameAsync(ArchiveOfferStatusName);

        this.ObjectMapper.Map(request, offer);
        await this.Data.SaveChangesAsync();
    }

    protected override async Task<IEnumerable<SortByExpressionDefinition<Offer>>> BuildDataCrudSortByExpression<TRequest>(
        TRequest request)
    {
        var sorters = new List<SortByExpressionDefinition<Offer>>();

        foreach (var sorter in request.SortBy)
        {
            if (sorter.Name == CosherConsts.OfferSortersNames.PriceDifferencePercent)
            {
                sorters.Add(ExpressionsBuilder.BuildPriceDifferencePercentSortByExpression<Offer>(sorter));
            } else if (sorter.Name == CosherConsts.OfferSortersNames.PriceDifferenceAmount)
            {
                sorters.Add(ExpressionsBuilder.BuildPriceDifferenceAmountSortByExpression<Offer>(sorter));
            }
            else
            {
        
                sorters.AddRange(
                    expressionsBuilder.BuildDataCrudSortByExpression<Offer, int>(
                        new[]
                        {
                            sorter
                        }
                    ));
                
                if (sorter.Name == "Price.Amount")
                {
                    DataSorter dateSorter = new DataSorter();
                    dateSorter.Name = "CreationTime";
                    dateSorter.Direction = DataSortDirection.Descending;
                    sorters.AddRange(expressionsBuilder.BuildDataCrudSortByExpression<Offer, int>(new[]
                    {
                        dateSorter
                    }));
                }
            }
        }

        return sorters;
    }


    public async Task ChangeOwnerAsync(ChangeOwnerRequestModel request)
    {
        var isEditGranted = await offerPermissionsChecker.IsEditGrantedAsync(
            request.EntityId,
            this.AbpSession.GetUserId());

        if (!isEditGranted)
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        var offer = this.Data.Offers.Include(o => o.Client).ThenInclude(c => c.ClientsSourceCategories)
            .FirstOrDefault(x => x.Id == request.EntityId);

        if (offer is null)
        {
            throw new UserFriendlyException($"Offer with id {request.EntityId} not found.");
        }

        var previousOfferClient = offer.Client;


        offer.ClientId = request.ClientId;
        await this.Data.SaveChangesAsync();

        await this.Data.Entry(offer)
            .Reference(o => o.Client)
            .LoadAsync();

        await this.Data.Entry(offer.Client)
            .Collection(c => c.ClientsSourceCategories)
            .LoadAsync();

        var relationWithPreviousClient =
            previousOfferClient.ClientsSourceCategories.First(csc => csc.EmployeeId == offer.EmployeeId);

        offer.Client.ClientsSourceCategories.Add(new ClientSourceCategory
        {
            EmployeeId = offer.EmployeeId,
            SourceCategoryId = relationWithPreviousClient.SourceCategoryId,
        });

        await this.Data.SaveChangesAsync();
    }

    public override async Task<int> UpdateAsync(int id, OfferUpdateRequestModel request)
    {
        var offer = await this
            .All()
            .Include(o => o.Estate)
            .ThenInclude(e => e.Address)
            .Include(o => o.Estate)
            .ThenInclude(e => e.HeatingSystems)
            .Include(o => o.Estate)
            .ThenInclude(e => e.FacingDirections)
            .Include(o => o.Estate)
            .ThenInclude(e => e.EstateDetail)
            .Include(o => o.Garages)
            .FirstOrDefaultAsync(o => o.Id == id);

        if (offer is null)
        {
            throw new UserFriendlyException("Offer with this id not found.");
        }
        var initialOfferStatus = offer.OfferStatusId;

        await this.HandleOfferEstateAddress(request, offer);

        HandleOfferPriceChange(request, offer);

        await this.Data.OfferDetails.Where(od => od.Id == id)
            .ExecuteDeleteAsync();

        await this.Data.OffersImages.Where(od => od.OfferId == id)
            .ExecuteDeleteAsync();

        await this.Data.OfferLifestyles.Where(od => od.OfferId == id)
            .ExecuteDeleteAsync();

        this.ObjectMapper.Map(request, offer);

        await HandleOfferGarages(request, offer);
        await HandleOfferEstateHeating(request, offer);
        await HandleOfferEstateFacingDirections(request, offer);

        this.Data.Offers.Update(offer);

        await this.Data.SaveChangesAsync();

        var draftOfferStatusId = await offerStatusesAppService.GetIdByNameAsync(DraftOfferStatusName);

        if (initialOfferStatus == draftOfferStatusId &&
            request.OfferStatusId is ActiveOfferStatusId or PotentialOfferStatusId)
        {
            if (offer.CmaAnalysesForBaseOffer.Count == 0 && offer.EmployeeId.HasValue)
            {
                var employee = await employeesAppService.GetEmployeeForNotificationById(offer.EmployeeId.Value);
                await this.SendGenerateSpaReminderNotificationAsync(offer.Id, employee);
            }
        }

        return id;
    }

    private async Task AddConnectionBetweenEmployeeAndClientAsync(Offer offer, int? employeeId)
    {
        if (employeeId == null)
        {
            return;
        }

        if (offer.Client.ClientsSourceCategories.Any(x => x.EmployeeId == employeeId))
        {
            return;
        }

        if (offer.EmployeeId == null)
        {
            return;
        }

        var relationWithPreviousClient =
            offer.Client.ClientsSourceCategories.First(csc => csc.EmployeeId == offer.EmployeeId);
        offer.Client.ClientsSourceCategories.Add(new ClientSourceCategory
        {
            EmployeeId = employeeId.Value,
            SourceCategoryId = relationWithPreviousClient.SourceCategoryId,
        });

        await this.Data.SaveChangesAsync();
    }

    private async Task<Offer> GetByIdAsync(int offerId)
    {
        var offer = await this.GetAsync(offerId);

        if (offer is null)
        {
            throw new UserFriendlyException($"Search with id {offerId} not found.");
        }

        return offer;
    }

    private async Task HandleOfferEstateFacingDirections(OfferUpdateRequestModel request, Offer offer)
    {
        offer.Estate.FacingDirections.Clear();
        var facingDirectionIds = request.EstateFacingDirections.Select(g => g.Id).ToList();
        var facingDirections = await this.Data.FacingDirections
            .Where(g => facingDirectionIds.Contains(g.Id))
            .ToListAsync();
        offer.Estate.FacingDirections.AddRange(facingDirections);
    }

    private async Task HandleOfferEstateHeating(OfferUpdateRequestModel request, Offer offer)
    {
        offer.Estate.HeatingSystems.Clear();
        var heatingIds = request.EstateHeatingSystems.Select(g => g.Id).ToList();
        var heating = await this.Data.Heating
            .Where(g => heatingIds.Contains(g.Id))
            .ToListAsync();
        offer.Estate.HeatingSystems.AddRange(heating);
    }

    private async Task HandleOfferGarages(OfferUpdateRequestModel request, Offer offer)
    {
        offer.Garages.Clear();
        var garageIds = request.Garages.Select(g => g.Id).ToList();
        var garages = await this.Data.Garages
            .Where(g => garageIds.Contains(g.Id))
            .ToListAsync();
        offer.Garages.AddRange(garages);
    }

    private static void HandleOfferPriceChange(OfferUpdateRequestModel request, Offer offer)
    {
        if (request.Price.Amount == offer.Price.Amount || offer.Price.Amount is 0)
            return;

        if (offer.OldPrice != null)
        {
            request.OldPrice ??= new MoneyRequestModel
            {
                Currency = Currency.EUR
            };
            request.OldPrice.Amount = offer.Price.Amount;
        }
        else
        {
            request.OldPrice = new MoneyRequestModel
            {
                Amount = offer.Price.Amount,
                Currency = Currency.EUR
            };
        }

        request.OfferPriceChanges.Add(new OfferPriceChangeRequestModel
        {
            OldPrice = new MoneyRequestModel
            {
                Amount = request.OldPrice.Amount,
                Currency = Currency.EUR
            },
            NewPrice = new MoneyRequestModel
            {
                Amount = request.Price.Amount,
                Currency = Currency.EUR
            }
        });
    }

    private async Task HandleOfferEstateAddress(OfferUpdateRequestModel request, Offer entity)
    {
        entity.Estate.Address.StreetId = request.Address?.StreetId;
        entity.Estate.Address.StreetNumber = request.Address?.StreetNumber;
        entity.Estate.Address.BlockNumber = request.Address?.BlockNumber;
        entity.Estate.Address.EntranceNumber = request.Address?.EntranceNumber;
        entity.Estate.Address.FloorNumber = request.Address?.FloorNumber;
        entity.Estate.Address.ApartmentNumber = request.Address?.ApartmentNumber;
        entity.Estate.Address.PopulatedPlaceId = request.Address?.PopulatedPlaceId;
        entity.Estate.Address.DistrictId = request.Address?.DistrictId;

        if (request.Address is { PopulatedPlaceId: not null })
        {
            var municipalityAndProvince =
                await populatedPlacesAppService.GetMunicipalityAndProvinceIdByPopulatedPlaceIdAsync(request
                    .Address.PopulatedPlaceId.Value);

            entity.Estate.Address.MunicipalityId = municipalityAndProvince.MunicipalityId;
            entity.Estate.Address.ProvinceId = municipalityAndProvince.ProvinceId;
        }
    }

    public async Task<IEnumerable<RelatedOfferResponseModel>> GetClientRelatedOffersAsync(int clientId)
        => await this.ObjectMapper
            .ProjectTo<RelatedOfferResponseModel>(this
                .AllAsNoTracking()
                .Where(o => o.ClientId == clientId)
                .OrderByDescending(x => x.CreationTime))
            .ToListAsync();

    public async Task<OfferClientEmployeeResponseModel?> GetClientAndEmployeeIdsAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<OfferClientEmployeeResponseModel>(this
                .AllAsNoTracking()
                .Where(o => o.Id == id))
            .FirstOrDefaultAsync();

    public async Task<OfferDealInfoResponseModel?> GetInfoForDealAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<OfferDealInfoResponseModel>(this
                .AllAsNoTracking()
                .Where(o => o.Id == id))
            .FirstOrDefaultAsync();

    public async Task<List<SearchClientRegisteredOfferResponseModel>> GetClientRegisteredSellOffersAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<SearchClientRegisteredOfferResponseModel>(this
                .AllAsNoTracking()
                .Where(this.BuildTenantSpecification())
                .Where(o =>
                    o.ClientId == clientId &&
                    o.EmployeeId != currentEmployeeId &&
                    o.OperationTypeId == SellOperationTypeId &&
                    o.OfferStatusId == DealOfferStatusId &&
                    o.Deals.Any(d =>
                        d.OfferId == o.Id &&
                        d.DealStatus == DealStatus.IsComplete &&
                        (d.EmployeeId == o.EmployeeId || d.OppositeSideEmployeeId == o.EmployeeId) &&
                        d.CreationTime >= dateTimeService.Now.SubtractYears(RegisteredOfferPastYears))))
            .ToListAsync();

    public async Task<OfferEmployeePermissionsResponseModel> GetEmployeeOfferPermissionsAsync(int clientId)
    {
        var permissions = new OfferEmployeePermissionsResponseModel();

        var currentEmployeeId = await this.EmployeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        var registeredOffersForPastYears = await this.GetClientRegisteredSellOffersAsync(
            clientId,
            currentEmployeeId);

        var registeredSearchesForPastYears = await this.GetClientRegisteredBuySearchesAsync(
            clientId,
            currentEmployeeId);

        var registeredOffersEmployeeIds = registeredOffersForPastYears
            .Select(o => o.EmployeeId)
            .ToList();

        var registeredSearchesEmployeeIds = registeredSearchesForPastYears
            .Select(o => o.EmployeeId)
            .ToList();

        var employeesIds = registeredOffersEmployeeIds.Concat(registeredSearchesEmployeeIds);

        var hasRegisteredCallForPastMonths = await callsAppService.HasClientCallsWithEmployeesAsync(
            clientId,
            employeesIds);

        var daysForRegisteringSellOfferString = await this.SettingManager.GetSettingValueAsync(
            AppSettings.UiManagement.Clients.RegisterOfferSellDaysSingleOffer);

        var daysForRegisteringSellOffer = int.Parse(daysForRegisteringSellOfferString);

        if (registeredOffersForPastYears.Any() &&
            registeredSearchesForPastYears.Any() &&
            hasRegisteredCallForPastMonths)
        {
            permissions.CanCreateSellOffer = false;

            return await this.HasOfferRentPermissionsAsync(
                permissions,
                clientId,
                currentEmployeeId,
                daysForRegisteringSellOffer);
        }

        return await this.HasOfferSellPermissionsAsync(
            permissions,
            clientId,
            currentEmployeeId,
            daysForRegisteringSellOffer);
    }

    private async Task<OfferEmployeePermissionsResponseModel> HasOfferRentPermissionsAsync(
        OfferEmployeePermissionsResponseModel permissions,
        int clientId,
        int? currentEmployeeId,
        int daysForRegisteringSellOffer)
    {
        var registeredRentOffers = await this.GetClientRegisteredRentOffersAsync(
            clientId,
            currentEmployeeId);

        var registeredRentOffersFromSearches = await this.GetClientRegisteredRentSearchOffersAsync(
            clientId,
            currentEmployeeId);

        var offers = registeredRentOffers.Concat(registeredRentOffersFromSearches);

        foreach (var offer in offers)
        {
            var months = offer.DealsCount == 1 ? 6 : 12;
            var dateSinceLastCall = dateTimeService.Now.SubtractMonths(months);

            var hasCallLastMonths = await callsAppService.HasClientCallsAfterDateAsync(
                clientId,
                offer.EmployeeId,
                dateSinceLastCall);

            if (hasCallLastMonths)
            {
                permissions = await this.HasOfferSellPermissionsAsync(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForRegisteringSellOffer);

                permissions.CanCreateRentOffer = true;
                permissions.CanCreateRentWithoutEstateId = offer.EstateId;

                return permissions;
            }
        }

        var activePotentialOffers = await this.GetActivePotentialRentOffersAsync(clientId, currentEmployeeId);

        foreach (var offer in activePotentialOffers)
        {
            var daysSinceOfferRegistration = dateTimeService.Now.Subtract(offer.CreationTime).Days;

            if (daysSinceOfferRegistration < daysForRegisteringSellOffer)
            {
                permissions = await this.HasOfferSellPermissionsAsync(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForRegisteringSellOffer);

                permissions.CanCreateRentOffer = true;
                permissions.CanCreateRentWithoutEstateId = offer.EstateId;

                return permissions;
            }

            if (offer is { Immunity: true, ImmunityEndDate: not null })
            {
                permissions = await this.HasOfferSellPermissionsAsync(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForRegisteringSellOffer);

                permissions.CanCreateRentOffer = true;
                permissions.CanCreateRentWithoutEstateId = offer.EstateId;

                return permissions;
            }

            var hasClientContract = await contractsAppService.HasClientContractForOfferAsync(clientId, offer.Id);

            if (hasClientContract)
            {
                permissions = await this.HasOfferSellPermissionsAsync(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForRegisteringSellOffer);

                permissions.CanCreateRentOffer = true;
                permissions.CanCreateRentWithoutEstateId = offer.EstateId;

                return permissions;
            }
        }

        permissions.CanCreateRentOffer = true;

        return permissions;
    }

    private async Task<OfferEmployeePermissionsResponseModel> HasOfferSellPermissionsAsync(
        OfferEmployeePermissionsResponseModel permissions,
        int clientId,
        int? currentEmployeeId,
        int daysForRegisteringSellOffer)
    {
        var clientActiveAndPotentialSellOffers = await this.GetActivePotentialSellOffersAsync(
            clientId,
            currentEmployeeId);

        if (!clientActiveAndPotentialSellOffers.Any())
        {
            permissions = await this.HasOfferRentPermissionsAsync(
                permissions,
                clientId,
                currentEmployeeId,
                daysForRegisteringSellOffer);

            permissions.CanCreateSellOffer = true;

            return permissions;
        }

        foreach (var offer in clientActiveAndPotentialSellOffers)
        {
            var daysSinceOfferRegistration = dateTimeService.Now.Subtract(offer.CreationTime).Days;

            if (daysSinceOfferRegistration < daysForRegisteringSellOffer)
            {
                permissions = await this.HasOfferRentPermissionsAsync(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForRegisteringSellOffer);

                permissions.CanCreateSellOffer = true;
                permissions.CanCreateSellWithoutEstateId = offer.EstateId;

                return permissions;
            }

            if (offer is { Immunity: true, ImmunityEndDate: not null })
            {
                permissions = await this.HasOfferRentPermissionsAsync(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForRegisteringSellOffer);

                permissions.CanCreateSellOffer = true;
                permissions.CanCreateSellWithoutEstateId = offer.EstateId;

                return permissions;
            }

            var hasClientContract = await contractsAppService.HasClientContractForOfferAsync(clientId, offer.Id);

            if (hasClientContract)
            {
                permissions = await this.HasOfferRentPermissionsAsync(
                    permissions,
                    clientId,
                    currentEmployeeId,
                    daysForRegisteringSellOffer);

                permissions.CanCreateSellOffer = true;
                permissions.CanCreateSellWithoutEstateId = offer.EstateId;

                return permissions;
            }
        }

        permissions.CanCreateSellOffer = true;

        return permissions;
    }

    public override async Task<int> CreateAsync(OfferCreateRequestModel request)
    {
        request.EmployeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());
        request.TenantId = this.GetTenantId();
        request.OfferStatusId = await offerStatusesAppService.GetIdByNameAsync(DraftOfferStatusName);

        var offer = this.ObjectMapper.Map<Offer>(request);

        if (request.EstateId is null or 0)
        {
            await this.CreateEstateForOfferAsync(request, offer);
        }

        await this.Data.Offers.AddAsync(offer);
        await this.Data.SaveChangesAsync();

        return offer.Id;
    }

    [Consumes("multipart/form-data")]
    public async Task UploadImagesAsync(int id, [FromForm] IFormFileCollection images)
    {
        var tenantId = this.GetTenantId();
        var estateImageCategoryId = await imageCategoriesAppService.GetIdByNameAsync(EstateImageCategoryName);

        await offersImagesAppService.UploadImagesAsync(id, tenantId, estateImageCategoryId, images);
    }

    public async Task<IEnumerable<OfferInListResponseModel?>> GetLastActiveOfferForClientsAsync(
        ICollection<int> clientIds)
    {
        var offers = await this.ObjectMapper.ProjectTo<OfferInListResponseModel>(this.AllAsNoTracking()
                .Where(x => clientIds.Contains(x.ClientId) && x.OfferStatusId == ActiveOfferStatusId))
            .ToListAsync();

        return offers.GroupBy(x => x.ClientId)
            .Select(g => g.MaxBy(s => s.LastModificationTime))
            .ToList();
    }

    public async Task<OfferActivityResponseModel?> GetActivityAsync(int offerId)
    {
        var response = await this.ObjectMapper
            .ProjectTo<OfferActivityResponseModel>(this.AllAsNoTracking().Where(o => o.Id == offerId))
            .FirstOrDefaultAsync();

        var viewings = await this.ObjectMapper
            .ProjectTo<ViewingResponseModel>(this.Data.Viewings.AsNoTracking()
                .Where(v => v.OfferId == offerId))
            .OrderByDescending(v => v.StartDate)
            .ToListAsync();

        var deals = await this.ObjectMapper
            .ProjectTo<DealShortResponseModel>(this.Data.Deals.AsNoTracking()
                .Where(v => v.OfferId == offerId))
            .OrderByDescending(v => v.CreationTime)
            .ToListAsync();

        if (response == null)
        {
            return response;
        }

        if (viewings.Count > 0)
        {
            response.Viewings.AddRange(viewings);
        }

        if (deals.Count > 0)
        {
            response.Deals.AddRange(deals);
        }

        return response;
    }

    public async Task<ICollection<TaskActivityResponseModel>> GetTasksAsync(int offerId)
        => await this.ObjectMapper
            .ProjectTo<TaskActivityResponseModel>(
                this.Data.OffersTasks.AsNoTracking()
                    .Where(ct => ct.OfferId == offerId)
            )
            .ToListAsync();

    public async Task OfferToEmployeeAsync(SuggestToEmployeeRequestModel request)
        => await this.SendOfferToEmployeeNotificationAsync(request);

    public async Task TransferOfferToEmployeeAsync(TransferToEmployeeRequestModel request)
    {
        var isEditGranted = await offerPermissionsChecker.IsEditGrantedAsync(
            request.EntityId,
            this.AbpSession.GetUserId());

        if (!isEditGranted)
        {
            throw new UserFriendlyException(this.L("NoPermission"));
        }

        var offer = this.Data.Offers.Include(o => o.Client).ThenInclude(c => c.ClientsSourceCategories)
            .FirstOrDefault(x => x.Id == request.EntityId);

        var newEmployee = this.Data.Employees
            .Include(employee => employee.UserAccount)
            .FirstOrDefault(e => e.Id == request.NewEmployeeId);

        if (newEmployee is null)
        {
            throw new UserFriendlyException($"Employee with ID: {request.NewEmployeeId} does not exists.");
        }

        if (offer is null)
        {
            throw new UserFriendlyException($"Offer with ID: {request.EntityId} does not exists.");
        }

        var offerChangeHistory = new OfferEmployeesChangeHistory
        {
            NewEmployeeId = newEmployee.Id,
            OldEmployeeId = offer.EmployeeId!.Value,
            OfferId = offer.Id,
            TenantId = this.AbpSession.GetTenantId(),
        };

        await this.AddConnectionBetweenEmployeeAndClientAsync(offer, request.NewEmployeeId);

        await this.Data.OffersEmployeesChangeHistory.AddAsync(offerChangeHistory);

        offer.EmployeeId = request.NewEmployeeId;
        await this.Data.SaveChangesAsync();

        await this.SendTransferredOfferNotificationAsync(
            offer.Id,
            newEmployee.UserAccount.TenantId!.Value,
            newEmployee.UserAccount.UserId);
    }

    public async Task DeleteImageAsync(OfferImageDeleteRequest request)
    {
        var offerImage = await this.Data.OffersImages
            .Include(x => x.Image)
            .ThenInclude(i => i.Thumbnails)
            .FirstOrDefaultAsync(x =>
                x.OfferId == request.OfferId && x.ImageId == request.ImageId);

        if (offerImage is null)
        {
            throw new UserFriendlyException("Offer image not found.");
        }

        offerImage.IsActive = false;
        offerImage.Image.IsDeleted = true;
        offerImage.Image.Thumbnails.ForEach(t => t.IsDeleted = true);

        this.Data.OffersImages.Remove(offerImage);
        await this.Data.SaveChangesAsync();
    }

    [HttpPost]
    public async Task<IEnumerable<OffersForDealResponseModel>> GetAllForDealCreationAsync(
        OffersForDealRequestModel request)
        => await this.ObjectMapper
            .ProjectTo<OffersForDealResponseModel>(this
                .AllAsNoTracking()
                .Where(new OfferByIdSpecification(request.Id)
                    .And(new OfferByOperationTypeSpecification(request.OperationType))
                    .And(new OfferByOwnershipStatusSpecification(request.OwnershipStatus, this.AbpSession.UserId)))
                .Where(this.BuildTenantSpecification())
                .OrderBy(o => o.Id)
                .Take(request.PageSize))
            .ToListAsync();

    [HttpPost]
    public async Task<IEnumerable<OfferEstateForProjectListingModel>> GetEstatesForProjectAsync(
        OfferEstateForProjectRequestModel request)
        => await this.ObjectMapper
            .ProjectTo<OfferEstateForProjectListingModel>(this
                .AllAsNoTracking()
                .Where(new OfferByProjectSpecification(request.ProjectId)
                    .And(new OfferByEstateTypeIdSpecification(request.EstateTypeId))
                    .And(new OfferByEstateBuildingSpecification(request.BuildingId))
                    .And(new OfferByEstateFloorNumberSpecification(request.FloorNumber))
                    .And(new OfferByEstateFacingDirectionSpecification(request.FacingDirectionId))
                    .And(new OfferByAreaSpecification(request.AreaFrom, request.AreaTo))
                    .And(new OfferByPriceSpecification(request.PriceFrom, request.PriceTo)))
                .Where(this.BuildTenantSpecification())
                .OrderByDescending(o => o.Id))
            .ToListAsync();

    public async Task UpdateContractType(int offerId)
    {
        var offer = await this.Data.Offers.Include(o => o.Contracts).FirstOrDefaultAsync(o => o.Id == offerId);

        if (offer is null)
        {
            throw new UserFriendlyException("Offer not found.");
        }

        offer.ContractTypeId = offer.Contracts.Any(x => x.ExpirationDate is null)
            ? offer.Contracts
                .FirstOrDefault(c => c.ExpirationDate is null)?.ContractTypeId
            : offer.Contracts
                .OrderByDescending(x => x.ExpirationDate).FirstOrDefault(c => c.ExpirationDate is not null)?.ContractTypeId;

        await this.Data.SaveChangesAsync();
    }

    private async Task SendOfferToEmployeeNotificationAsync(SuggestToEmployeeRequestModel request)
    {
        var notificationUser = new UserIdentifier(
            this.AbpSession.TenantId,
            request.EmployeeUserId);

        var currentEmployee = await this.EmployeesAppService.GetByUserId(this.AbpSession.UserId);

        if (currentEmployee is null)
        {
            throw new UserFriendlyException("Current user is not a employee.");
        }

        await appNotifier.SendImportantMessageAsync(
            notificationUser,
            this.L("NewOfferToEmployeeSuggestion", request.EntityId,
                currentEmployee.FirstName + " " + currentEmployee.LastName));
    }

    private async Task CreateEstateForOfferAsync(OfferCreateRequestModel request, Offer offer)
    {
        var populatedPlaceInfo = await populatedPlacesAppService.GetPopulatedPlaceInfoAsync(request.PopulatedPlaceId);

        if (populatedPlaceInfo is null)
        {
            throw new EntityNotFoundException(typeof(PopulatedPlace), request.PopulatedPlaceId);
        }

        populatedPlaceInfo.DistrictId = request.DistrictId;

        var estateAddress = this.ObjectMapper.Map<Address>(populatedPlaceInfo);

        estateAddress.TenantId = this.GetTenantId();

        offer.Estate = new Estate
        {
            TypeId = request.EstateTypeId,
            CategoryId = request.EstateCategoryId,
            Address = estateAddress,
            TenantId = this.GetTenantId()
        };
    }

    protected override IQueryable<Offer> BuildDetailsQuery(int id)
    {
        return base.BuildDetailsQuery(id).Include(o => o.OfferDetail);
    }

    private async Task<IEnumerable<RegisteredRentOfferResponseModel>> GetClientRegisteredRentOffersAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<RegisteredRentOfferResponseModel>(this
                .AllAsNoTracking()
                .Where(this.BuildTenantSpecification())
                .Where(o =>
                    o.ClientId == clientId &&
                    o.EmployeeId != currentEmployeeId &&
                    o.OperationTypeId == RentOperationTypeId &&
                    o.OfferStatusId == DealOfferStatusId))
            .ToListAsync();

    private async Task<IEnumerable<RegisteredRentOfferResponseModel>> GetClientRegisteredRentSearchOffersAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<RegisteredRentOfferResponseModel>(this
                .Data
                .Searches
                .AsNoTracking()
                .Where(s =>
                    s.ClientId == clientId &&
                    s.EmployeeId != currentEmployeeId &&
                    s.Type == SearchType.Rent &&
                    s.SearchStatusId == DealSearchStatusId &&
                    s.Matches.Any(m => m.SearchId == s.Id))
                .SelectMany(s => s.Matches)
                .Where(m => m.StatusId == (int) MatchStatusId.Preliminary || m.StatusId == (int) MatchStatusId.Confession)
                .Select(m => m.Offer)
                .Where(this.BuildTenantSpecification())
                .Where(o =>
                    o.ClientId == clientId &&
                    o.EmployeeId != currentEmployeeId &&
                    o.OperationTypeId == RentOperationTypeId &&
                    o.OfferStatusId == DealOfferStatusId))
            .ToListAsync();

    private async Task<List<ActivePotentialOfferResponseModel>> GetActivePotentialSellOffersAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<ActivePotentialOfferResponseModel>(this
                .AllAsNoTracking()
                .Where(this.BuildTenantSpecification())
                .Where(o =>
                    o.ClientId == clientId &&
                    o.EmployeeId != currentEmployeeId &&
                    o.OperationTypeId == SellOperationTypeId &&
                    (o.OfferStatusId == PotentialOfferStatusId || o.OfferStatusId == ActiveOfferStatusId)))
            .ToListAsync();

    private async Task<List<ActivePotentialOfferResponseModel>> GetActivePotentialRentOffersAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<ActivePotentialOfferResponseModel>(this
                .AllAsNoTracking()
                .Where(this.BuildTenantSpecification())
                .Where(o =>
                    o.ClientId == clientId &&
                    o.EmployeeId != currentEmployeeId &&
                    o.OperationTypeId == RentOperationTypeId &&
                    (o.OfferStatusId == PotentialOfferStatusId || o.OfferStatusId == ActiveOfferStatusId)))
            .ToListAsync();

    private async Task<List<SearchClientRegisteredSearchResponseModel>> GetClientRegisteredBuySearchesAsync(
        int clientId,
        int? currentEmployeeId)
        => await this.ObjectMapper
            .ProjectTo<SearchClientRegisteredSearchResponseModel>(this
                .Data
                .Searches
                .AsNoTracking()
                .Where(s =>
                    s.ClientId == clientId &&
                    s.EmployeeId != currentEmployeeId &&
                    s.Type == SearchType.Buy &&
                    s.SearchStatusId == DealSearchStatusId &&
                    s.Deals.Any(d =>
                        d.SearchId == s.Id &&
                        d.DealStatus == DealStatus.IsComplete &&
                        (d.EmployeeId == s.EmployeeId || d.OppositeSideEmployeeId == s.EmployeeId) &&
                        d.CreationTime >= dateTimeService.Now.SubtractYears(RegisteredOfferPastYears))))
            .ToListAsync();

    private static int GetSortPriority(int offerStatus)
    {
        return offerStatus switch
        {
            (int) OfferStatuses.Active => 1,
            (int) OfferStatuses.Potential => 2,
            (int) OfferStatuses.Draft => 3,
            _ => 4,
        };
    }

    private async Task SendGenerateSpaReminderNotificationAsync(
        int offerId,
        EmployeeForNotificationResponseModel? employee)
    {
        if (employee?.UserId is null)
        {
            return;
        }

        var notificationUser = new UserIdentifier(
            employee.TenantId,
            employee.UserId.Value);

        var message = this.L("GenerateSpaNotificationMessage", offerId);

        await appNotifier.SendGoalMessageAsync(
            notificationUser,
            message);
    }

    private async Task SendTransferredOfferNotificationAsync(
        int offerId,
        int employeeTenantId,
        long employeeUserId)
    {
        var notificationUser = new UserIdentifier(
            employeeTenantId,
            employeeUserId);

        var message = this.L("NewOfferTransferredNotificationMessage", offerId);

        await appNotifier.SendImportantMessageAsync(
            notificationUser,
            message);
    }


    public async Task<IEnumerable<OfferLastPriceChangeModel>>
        GetOffersWithPriceChangesAsync(OfferByPriceChangeRequestModel request)
    {
        var query = this.Data.Offers
            .AsNoTracking()
            .Where(new OfferByPriceChangeSpecification()
                .And(new OffersByOperationTypeSpecification(request.OperationTypeId)))
            .Take(request.PageSize);

        var projected = this.ObjectMapper
            .ProjectTo<OfferLastPriceChangeModel>(query)
            .OrderByDescending(x => x.PriceDifferencePercent)
            .ThenByDescending(x => x.CreationTime);

        var result = await projected.ToListAsync();
        return result;
    }

}