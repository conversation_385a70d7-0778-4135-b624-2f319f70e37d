using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.Addresses.Models;
using RealtoCrm.DataCrudModels;
using RealtoCrm.Mapping;
using RealtoCrm.Money;
using RealtoCrm.Images.Models;

namespace RealtoCrm.Offers.Models;

public class OfferLastPriceChangeModel : IMapFrom<Offer>, IMapExplicitly
{
    public int Id { get; init; }
    public DateTime CreationTime { get; init; }

    public MoneyResponseModel Price { get; init; } = default!;
    
    public MoneyResponseModel? OldPrice { get; init; }
    public IEnumerable<ImageSmallResponseModel> OffersImages { get; init; } = [];
    
    public string OperationTypeName { get; init; } = default!;
    
    public string EstateTypeName { get; init; } = default!;
    public double? EstateArea { get; init; }
    
    public bool? HasKey { get; init; }
    
    public Money.Money? SquareMetrePrice { get; set; }
    
    public AddressResponseModel EstateAddress { get; init; } = default!;
    public MoneyResponseModel? MaxOfferedPriceFromDeposits { get; set; }
    public decimal? PriceDifferencePercent { get; set; }

    public virtual void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Offer, OfferLastPriceChangeModel>()
            .ForMember(dest => dest.MaxOfferedPriceFromDeposits,
                opt
                    => opt.MapFrom(src => src.Deposits.Count > 0
                        ? src.Deposits.OrderByDescending(s => s.OfferedPrice.Amount).First().OfferedPrice
                        : null)
            )
            .ForMember(dest => dest.EstateArea,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Area.HasValue ? (double?)Math.Round(src.Estate.Area.Value, 2) : null))
            .ForMember(slm => slm.OffersImages, cfg => cfg
                .MapFrom(s => s.OffersImages
                    .Where(oi => oi.IsActive)
                    .OrderBy(oi => oi.Order)))
            .ForMember(dest => dest.PriceDifferencePercent,  cfg 
                => cfg.MapFrom(dest => Math.Abs(dest.Price.Amount - dest.OldPrice.Amount) / dest.OldPrice.Amount * 100));
}

