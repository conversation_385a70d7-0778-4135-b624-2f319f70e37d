namespace RealtoCrm.Offers.Models;

using System;
using AutoMapper;
using Mapping;
using Money;

public class ImportOfferRequestModel : IMapTo<Offer>, IMapExplicitly
{
    public string? Name { get; init; }

    public string? EstateDescription { get; init; }

    public string? BuildingDescription { get; init; }

    public string? LocationDescription { get; init; }

    public string? AdvantagesDescription { get; init; }

    public string? DistributionDescription { get; init; }

    public string? ConvenientTimeToView { get; init; }

    public decimal? PriceAmount { get; init; }

    public string? PriceCurrency { get; init; }

    public decimal? OldPriceAmount { get; init; }

    public string? OldPriceCurrency { get; init; }

    public decimal? RecommendedAmount { get; init; }

    public string? RecommendedCurrency { get; init; }

    public decimal? SquareMetreAmount { get; init; }

    public string? SquareMetreCurrency { get; init; }

    public decimal? ComparativeMarketAnalysisAmount { get; init; }

    public string? ComparativeMarketAnalysisCurrency { get; init; }

    public decimal? MaintenanceFeeAmount { get; init; }

    public string? MaintenanceFeeCurrency { get; init; }

    public bool PriceOnRequest { get; init; } = false;

    public bool? HasKey { get; init; }

    public int ClientId { get; init; }

    public int EstateId { get; init; }

    public int OperationTypeId { get; init; }

    public int? BuildingId { get; init; }

    public int? EstateGroupId { get; init; }

    public int? ProjectId { get; init; }

    public int? FurnitureId { get; init; }

    public int? VatId { get; init; }

    public int? LifestyleId { get; init; }

    public int? ContractTypeId { get; init; }

    public int? GarageId { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<ImportOfferRequestModel, Offer>()
            .ForMember(m => m.Price, cfg => cfg
                .MapFrom(m => new Money
                {
                    Amount = m.PriceAmount ?? decimal.Zero,
                    Currency = ParseCurrency(m.PriceCurrency)
                }))
            .ForMember(m => m.OldPrice, cfg => cfg
                .MapFrom(m => new Money
                {
                    Amount = m.OldPriceAmount ?? decimal.Zero,
                    Currency = ParseCurrency(m.OldPriceCurrency)
                }))
            .ForMember(m => m.RecommendedPrice, cfg => cfg
                .MapFrom(m => new Money
                {
                    Amount = m.RecommendedAmount ?? decimal.Zero,
                    Currency = ParseCurrency(m.RecommendedCurrency)
                }))
            .ForMember(m => m.SquareMetrePrice, cfg => cfg
                .MapFrom(m => new Money
                {
                    Amount = m.SquareMetreAmount ?? decimal.Zero,
                    Currency = ParseCurrency(m.SquareMetreCurrency)
                }))
            .ForMember(m => m.ComparativeMarketAnalysisPrice, cfg => cfg
                .MapFrom(m => new Money
                {
                    Amount = m.ComparativeMarketAnalysisAmount ?? decimal.Zero,
                    Currency = ParseCurrency(m.ComparativeMarketAnalysisCurrency)
                }))
            .ForMember(m => m.MaintenanceFee, cfg => cfg
                .MapFrom(m => new Money
                {
                    Amount = m.MaintenanceFeeAmount ?? decimal.Zero,
                    Currency = ParseCurrency(m.MaintenanceFeeCurrency)
                }));

    private static Currency ParseCurrency(string? currency)
        => string.IsNullOrWhiteSpace(currency)
            ? Currency.BGN
            : Enum.Parse<Currency>(currency);
}