namespace RealtoCrm.Offers.Models;

using DataCrudModels;
using Models = Common.Models;

public class OffersPaginatedRequest : PaginatedRequestModel
{
    public int? Id { get; init; }

    public string? SearchTerm { get; init; }

    public int? EmployeeId { get; init; }

    public int? AreaFrom { get; set; }

    public int? AreaTo { get; set; }

    public int? MoneyFrom { get; set; }

    public int? MoneyTo { get; set; }

    public int? SquareMetrePriceFrom { get; set; }

    public int? SquareMetrePriceTo { get; set; }

    public int? CurrentUserEmployeeId { get; init; }

    public OfferEstateTypeFilteringModel? EstateType { get; set; } = new();

    public string? OperationType { get; set; }

    public int? DepositId { get; init; }

    public Models.OwnershipStatus? OwnershipStatus { get; set; } = Models.OwnershipStatus.All;

    public AddressFilteringModel? AddressFilteringModel { get; set; } = new();

    public OfferAdvancedFiltersFilteringModel? AdvancedFilters { get; set; } = new();
}