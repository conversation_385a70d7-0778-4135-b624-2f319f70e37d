namespace RealtoCrm.Offers.Models;

using System;
using System.Linq;
using Addresses.Models;
using AutoMapper;
using Mapping;
using Money;
using static CosherConsts.Clients;

public class RelatedOfferResponseModel : IMapFrom<Offer>, IMapExplicitly
{
    public int Id { get; init; }

    public int ClientId { get; init; }

    public string ClientFirstName { get; init; } = default!;

    public string ClientLastName { get; init; } = default!;

    public int EstateId { get; init; }

    public int EstateTypeId { get; init; }

    public string EstateTypeName { get; init; } = default!;

    public int EstateCategoryId { get; init; }

    public string EstateCategoryName { get; init; } = default!;

    public int ParentEstateCategoryId { get; init; }

    public string ParentEstateCategoryName { get; init; } = default;

    public AddressResponseModel EstateAddress { get; init; } = default!;

    public int? EstateBedroomsCount { get; init; }

    public int? EstateBathroomsCount { get; init; }

    public int? EstateTerracesCount { get; init; }

    public int? OfferStatusId { get; init; }

    public string? OfferStatusName { get; init; }

    public double? EstateArea { get; init; }

    public string EmployeeFullName { get; init; } = default!;

    public MoneyResponseModel Price { get; init; } = default!;

    public bool HasViewings { get; init; }


    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Offer, RelatedOfferResponseModel>()
            .ForMember(m => m.ClientFirstName, cfg => cfg
                .MapFrom(src => src.Client.TypeId == PersonalClientId
                    ? src.Client.PersonalData!.FirstName
                    : src.Client.LegalEntity!.Name))
            .ForMember(m => m.ClientLastName, cfg => cfg
                .MapFrom(src => src.Client.TypeId == PersonalClientId
                    ? src.Client.PersonalData!.LastName
                    : string.Empty))
            .ForMember(dest => dest.EstateArea,
                opt
                    => opt.MapFrom(src =>
                        src.Estate.Area.HasValue ? (double?)Math.Round(src.Estate.Area.Value, 2) : null))
            .ForMember(
                dest => dest.ParentEstateCategoryId,
                opt => opt.MapFrom(
                    src => src.Estate.Category.ParentId))
            .ForMember(dest => dest.EmployeeFullName, cfg => cfg
                .MapFrom(opt => opt.Employee != null
                    ? $"{opt.Employee.FirstName} {opt.Employee.LastName}".Trim()
                    : null))
            .ForMember(
                dest => dest.ParentEstateCategoryName,
                opt => opt.MapFrom(
                    src => src.Estate.Category.ParentId.HasValue
                        ? src.Estate.Category.Parent.Name
                        : null))
            .ForMember(dest => dest.HasViewings, opt
                => opt.MapFrom(src => src.Viewings.Count != 0));
}