namespace RealtoCrm.Authorization.Users;

using System;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Employees;
using EntityFrameworkCore;
using MultiTenancy;
using MultiTenancy.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp;
using Abp.Authorization;
using Abp.Authorization.Users;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Notifications;
using Abp.Runtime.Session;
using Abp.UI;
using Caching;
using Common.Attributes;
using Common.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Permissions;
using RealtoCrm.Authorization.Permissions.Dto;
using Roles;
using Dto;
using DataCrudModels;
using Expressions;
using Models;
using Notifications;
using Profile.Dto;
using Url;
using Specifications;

public class UserAppService(
    RoleManager roleManager,
    IRoleAppService roleAppService,
    IUserEmailer userEmailer,
    ICachingAppService cachingAppService,
    IPermissionChecker permissionChecker,
    INotificationSubscriptionManager notificationSubscriptionManager,
    IAppNotifier appNotifier,
    IUserPolicy userPolicy,
    IUserLinkManager userLinkManager,
    IEnumerable<IPasswordValidator<User>> passwordValidators,
    IPasswordHasher<User> passwordHasher,
    UserManager userManager,
    IUserLinkAppService userLinkAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        long,
        User,
        UserCreateRequestModel,
        UserUpdateRequestModel,
        UsersPaginatedRequestModel,
        UserDetailsResponseModel,
        UserListingResponseModel>(dbContextProvider, expressionsBuilder), IUserAppService
{
    public IAppUrlService AppUrlService { get; set; } = NullAppUrlService.Instance;

    protected override bool IgnoreQueryFilters => true;

    private RealtoCrmDbContext Db => dbContextProvider.GetDbContext();

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateUserAsync), AppPermissions.UserCreate)
            .ConfigureAuthorize(nameof(this.CreateUserForTenantAsync), AppPermissions.UserCreate)
            .ConfigureAuthorize(nameof(this.UpdateUserAsync), AppPermissions.UserUpdate)
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.UserDelete)
            .ConfigureAuthorize(
                nameof(this.GetDetailsAsync),
                AppPermissions.UserRead,
                AppPermissions.UserRoleRead,
                AppPermissions.UserRoleUpdate)
            .ConfigureAuthorize(nameof(this.ResetUserSpecificPermissions), AppPermissions.UserRoleUpdate)
            .ConfigureAuthorize(nameof(this.UpdateUserPermissions), AppPermissions.UserRoleUpdate)
            .ConfigureAuthorize(nameof(this.UnlockUser), AppPermissions.UserUpdate);

    public override async Task<UserDetailsResponseModel?> GetDetailsAsync(long id)
    {
        var user = await base.GetDetailsAsync(id);

        if (user is null)
        {
            return null;
        }

        using (this.UnitOfWorkManager.Current.EnableFilter(AbpDataFilters.MayHaveTenant, AbpDataFilters.MustHaveTenant))
        using (this.UnitOfWorkManager.Current.SetTenantId(user.TenantId))
        using (this.UnitOfWorkManager.Current.DisableFilter(AbpDataFilters.SoftDelete))
        {
            user.Roles = await roleAppService.GetAllByUserId(id);
            user.LinkedUsers = await userLinkAppService.GetLinkedUsersAsync(id);

            var userEntity = await this.GetByIdAsync(id);
            var permissions = this.PermissionManager.GetAllPermissions();
            var grantedPermissions = await this.UserManager.GetGrantedPermissionsAsync(userEntity);

            user.Permissions = this.ObjectMapper
                .Map<List<FlatPermissionDto>>(permissions)
                .ToList();

            user.CustomUserPermissions = await this
                .Data
                .UserPermissions
                .Where(p => p.UserId == id)
                .Select(p => p.Name)
                .ToListAsync();

            user.GrantedPermissionNames = grantedPermissions
                .Select(p => p.Name)
                .ToList();

            user.GrantedRolePermissionNames = await roleManager.GetGrantedPermissionNamesAsync(
                user.Roles.Select(r => r.Name));

            return user;
        }
    }

    public override async Task<bool> DeleteAsync(long id)
    {
        if (this.AbpSession.GetUserId() == id)
        {
            throw new UserFriendlyException(this.L("YouCanNotDeleteOwnAccount"));
        }

        var user = await this.GetByIdAsync(id);

        if (this.IsGlobalAdministrator(user))
        {
            throw new UserFriendlyException(this.L("YouCanNotDeleteGlobalAdminAccount"));
        }

        var employee = await this.Data.Employees.FirstOrDefaultAsync(e => e.UserAccount.UserId == id);

        if (employee is not null)
        {
            this.Data.Remove(employee);
        }

        user.IsActive = false;

        using (this.UnitOfWorkManager.Current.SetTenantId(user.TenantId))
        {
            this.CheckErrors(await this.UserManager.UpdateAsync(user));
            this.CheckErrors(await this.UserManager.DeleteAsync(user));
        }

        return true;
    }

    public async Task<IEnumerable<UserActiveDetailsResponseModel>> GetUsersActiveDetailsAsync(
        IEnumerable<long> userIds)
        => await this.ObjectMapper
            .ProjectTo<UserActiveDetailsResponseModel>(this
                .AllAsNoTracking()
                .IgnoreQueryFilters()
                .Where(u => userIds.Contains(u.Id)))
            .ToListAsync();

    public async Task ResetUserSpecificPermissions(long userId)
    {
        var user = await this.GetByIdAsync(userId);

        using (this.UnitOfWorkManager.Current.SetTenantId(user.TenantId))
        {
            await this.UserManager.ResetAllPermissionsAsync(user);
        }
    }

    public async Task UpdateUserPermissions(UpdateUserPermissionsInput input)
    {
        using (this.UnitOfWorkManager.Current.EnableFilter(AbpDataFilters.MayHaveTenant, AbpDataFilters.MustHaveTenant))
        using (this.UnitOfWorkManager.Current.SetTenantId(input.TenantId))
        {
            var user = await this.GetByIdAsync(input.Id);

            var grantedPermissions = this.PermissionManager.GetPermissionsFromNamesByValidating(
                input.GrantedPermissionNames);

            await this.UserManager.SetGrantedPermissionsAsync(user, grantedPermissions);

            await cachingAppService.ClearPermissionsCache();
        }
    }

    public async Task DeleteAllCustomPermissions(long userId, int? tenantId)
    {
        var userPermissions = await this
            .Data
            .UserPermissions
            .Where(p => p.UserId == userId && p.TenantId == tenantId)
            .ToListAsync();

        this.Data.RemoveRange(userPermissions);

        await this.Data.SaveChangesAsync();

        await cachingAppService.ClearPermissionsCache();
    }

    public async Task CreateOrUpdateUser(CreateOrUpdateUserInput input)
    {
        if (input.User.Id.HasValue)
        {
            await this.UpdateUserAsync(input);
        }
        else
        {
            await this.CreateUserAsync(input);
        }
    }

    public async Task UnlockUser(long userId)
    {
        var user = await this.GetByIdAsync(userId);

        user.Unlock();
    }

    public async Task ChangeProfilePictureUrlAsync(long userId, string profilePictureUrl)
    {
        var user = await userManager.GetUserByIdAsync(userId);

        user.ProfilePictureUrl = profilePictureUrl;

        await userManager.UpdateAsync(user);
    }

    public async Task<User> CreateUserAsync(Tenant tenant, UserCreateRequestModel request)
    {
        var (user, _) = await this.CreateUserAndEmployeeInternalAsync(tenant, request, createInitialRoles: true);

        return user;
    }

    public async Task<User> CreateUserForTenantAsync(int? tenantId, UserCreateRequestModel request)
    {
        var tenant = await this.Db.Tenants.FirstOrDefaultAsync(t => t.Id == tenantId);

        var (user, _) = await this.CreateUserAndEmployeeInternalAsync(tenant, request);

        return user;
    }

    public async Task<User> UpdateUserAsync(int? tenantId, long userId, UserUpdateRequestModel userRequest)
    {
        using (this.UnitOfWorkManager.Current.EnableFilter(AbpDataFilters.MayHaveTenant, AbpDataFilters.MustHaveTenant))
        using (this.UnitOfWorkManager.Current.SetTenantId(tenantId))
        {
            var user = await this.GetByIdAsync(userId);

            if (user is null)
            {
                throw new EntityNotFoundException(this.L("UserNotFound"));
            }

            var userAccount = await userLinkManager.GetUserAccountAsync(user.ToUserIdentifier());

            user.TenantId = tenantId;
            userAccount.TenantId = tenantId;

            var roles = await roleManager.GetRoleNamesByIds(userRequest.RoleIds);

            this.CheckErrors(await this.UserManager.SetRolesAsync(user, roles));

            await userLinkAppService.LinkToUsersAsync(new LinkToUsersRequestModel
            {
                UserId = userId,
                LinkedUsers = userRequest.LinkedUsers,
            });

            return user;
        }
    }

    public async Task<IEnumerable<UserProfilePictureResponseModel>> GetProfilePicturesByUsersAsync(
        IEnumerable<long> userIds)
        => await this.ObjectMapper
            .ProjectTo<UserProfilePictureResponseModel>(this
                .AllAsNoTracking()
                .IgnoreQueryFilters()
                .Where(u => userIds.Contains(u.Id)))
            .ToListAsync();

    public async Task<(User, Employee?)> CreateWithEmployeeForTenantAsync(
        Tenant? tenant,
        UserCreateRequestModel userRequest,
        EmployeeRequestModel employeeRequest)
        => await this.CreateUserAndEmployeeInternalAsync(tenant, userRequest, employeeRequest);

    public async Task<long> UpdateUserForProfileAsync(long userId, CurrentUserProfileEditDto editModel)
    {
        var user = await this.GetByIdAsync(userId);

        if (user is null)
        {
            throw new EntityNotFoundException($"User with id '{userId}' is not found.");
        }

        if (user.UserName != editModel.UserName || user.EmailAddress != editModel.EmailAddress)
        {
            var userUpdateModel = this.ObjectMapper.Map<UserUpdateProfileRequestModel>(editModel);
            this.ObjectMapper.Map(userUpdateModel, user);

            await this.Data.SaveChangesAsync();
        }

        return user.Id;
    }

    protected override async Task<Specification<User>> GetSpecificationAsync(UsersPaginatedRequestModel request)
        => new UserOnlyActiveSpecification(request.OnlyActive)
            .And(new UserByEmailSpecification(request.Email))
            .And(new UserByTenantSpecification(
                request.OnlyInCurrentTenant,
                await permissionChecker.IsGrantedAsync(AppPermissions.UserRead),
                this.AbpSession.TenantId));

    protected virtual async Task UpdateUserAsync(CreateOrUpdateUserInput input)
    {
        Debug.Assert(input.User.Id != null, "input.User.Id should be set.");

        var user = await this.UserManager.FindByIdAsync(input.User.Id.Value.ToString());

        if (user is null)
        {
            throw new AbpException(this.L("UserNotFound"));
        }

        var isEmailChanged = user.EmailAddress != input.User.EmailAddress;

        if (isEmailChanged)
        {
            user.IsEmailConfirmed = false;
        }

        //Update user properties
        this.ObjectMapper.Map(input.User, user); //Passwords is not mapped (see mapping configuration)

        this.CheckErrors(await this.UserManager.UpdateAsync(user));

        if (input.SetRandomPassword)
        {
            var randomPassword = await userManager.CreateRandomPassword();
            user.Password = passwordHasher.HashPassword(user, randomPassword);
            input.User.Password = randomPassword;
        }
        else if (!input.User.Password.IsNullOrEmpty())
        {
            await this.UserManager.InitializeOptionsAsync(this.AbpSession.TenantId);
            this.CheckErrors(await this.UserManager.ChangePasswordAsync(user, input.User.Password));
        }

        //Update roles
        this.CheckErrors(await this.UserManager.SetRolesAsync(user, input.AssignedRoleNames));

        //update organization units
        await this.UserManager.SetOrganizationUnitsAsync(user, input.OrganizationUnits.ToArray());

        if (input.SendActivationEmail || isEmailChanged)
        {
            user.SetNewEmailConfirmationCode();
            await userEmailer.SendEmailActivationLinkAsync(
                user, this.AppUrlService.CreateEmailActivationUrlFormat(this.AbpSession.TenantId),
                input.User.Password
            );
        }
    }

    protected virtual async Task CreateUserAsync(CreateOrUpdateUserInput input)
    {
        if (this.AbpSession.TenantId.HasValue)
        {
            await userPolicy.CheckMaxUserCountAsync(this.AbpSession.GetTenantId());
        }

        var user = this.ObjectMapper.Map<User>(input.User); //Passwords is not mapped (see mapping configuration)
        user.TenantId = this.AbpSession.TenantId;

        //Set password
        if (input.SetRandomPassword)
        {
            var randomPassword = await userManager.CreateRandomPassword();
            user.Password = passwordHasher.HashPassword(user, randomPassword);
            input.User.Password = randomPassword;
        }
        else if (!input.User.Password.IsNullOrEmpty())
        {
            await this.UserManager.InitializeOptionsAsync(this.AbpSession.TenantId);
            foreach (var validator in passwordValidators)
            {
                this.CheckErrors(await validator.ValidateAsync(this.UserManager, user, input.User.Password));
            }

            user.Password = passwordHasher.HashPassword(user, input.User.Password);
        }

        user.ShouldChangePasswordOnNextLogin = input.User.ShouldChangePasswordOnNextLogin;

        //Assign roles
        user.Roles = new Collection<UserRole>();
        foreach (var roleName in input.AssignedRoleNames)
        {
            var role = await roleManager.GetRoleByNameAsync(roleName);
            user.Roles.Add(new UserRole(this.AbpSession.TenantId, user.Id, role.Id));
        }

        this.CheckErrors(await this.UserManager.CreateAsync(user));
        await this.CurrentUnitOfWork.SaveChangesAsync(); //To get new user's Id.

        //Notifications
        await notificationSubscriptionManager.SubscribeToAllAvailableNotificationsAsync(user.ToUserIdentifier());
        await appNotifier.WelcomeToTheApplicationAsync(user);

        //Organization Units
        await this.UserManager.SetOrganizationUnitsAsync(user, input.OrganizationUnits.ToArray());

        //Send activation email
        if (input.SendActivationEmail)
        {
            user.SetNewEmailConfirmationCode();
            await userEmailer.SendEmailActivationLinkAsync(
                user, this.AppUrlService.CreateEmailActivationUrlFormat(this.AbpSession.TenantId),
                input.User.Password
            );
        }
    }

    protected override async Task<IEnumerable<UserListingResponseModel>> GetListingAsync(
        Specification<User> specification,
        Expression<Func<User, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<User>> sortByExpressions,
        int skip = 0,
        int take = int.MaxValue)
        => await this
            .AllAsNoTracking()
            .IgnoreQueryFilters(this.IgnoreQueryFilters)
            .Where(filtersExpression)
            .Where(specification)
            .ApplySortByDefinitions(sortByExpressions)
            .Select(u => new UserListingResponseModel
            {
                Id = u.Id,
                UserName = u.UserName,
                EmailAddress = u.EmailAddress,
                IsActive = u.IsActive,
                IsEmailConfirmed = u.IsEmailConfirmed,
                CreationTime = u.CreationTime,
                ProfilePictureId = u.ProfilePictureId,
                TenantId = u.TenantId,
                EmployeeId = this
                    .Data
                    .Employees
                    .AsNoTracking()
                    .Where(e => e.UserAccount.UserId == u.Id)
                    .Select(e => (int?)e.Id)
                    .FirstOrDefault()
            })
            .Skip(skip)
            .Take(take)
            .ToListAsync();

    private async Task<(User, Employee?)> CreateUserAndEmployeeInternalAsync(
        Tenant? tenant,
        UserCreateRequestModel userRequest,
        EmployeeRequestModel? employeeRequest = null,
        bool createInitialRoles = false)
    {
        using (this.UnitOfWorkManager.Current.EnableFilter(AbpDataFilters.MayHaveTenant, AbpDataFilters.MustHaveTenant))
        using (this.UnitOfWorkManager.Current.SetTenantId(tenant?.Id))
        {
            var roles = createInitialRoles && tenant is not null
                ? [await roleManager.GetRoleByNameAsync(StaticRoleNames.Tenants.Admin)]
                : await roleManager.GetRolesByIds(userRequest.RoleIds);

            var user = await this.CreateUserInternal(tenant?.Id, userRequest, roles);

            var userAccount = await this.CreateUserAccountAsync(tenant?.Id, user);

            Employee? employee = null;

            if (employeeRequest is not null)
            {
                employee = await this.CreateEmployeeInternal(tenant?.Id, employeeRequest, userAccount);
            }

            await this.UnitOfWorkManager.Current.SaveChangesAsync();

            return (user, employee);
        }
    }

    private async Task<User> CreateUserInternal(
        int? tenantId,
        UserCreateRequestModel userModel,
        IEnumerable<Role> roles)
    {
        var user = this.ObjectMapper.Map<User>(userModel);
        user.TenantId = tenantId;

        await userManager.InitializeOptionsAsync(this.AbpSession.TenantId);
        foreach (var validator in userManager.PasswordValidators)
        {
            this.CheckErrors(await validator.ValidateAsync(userManager, user, user.Password));
        }

        await this.CheckUniqueEmailForTenant(tenantId, userModel);

        user.SetNormalizedNames();

        var result = await userManager.CreateAsync(user, userModel.Password);

        this.CheckErrors(result);

        var roleNames = roles.Select(r => r.Name).ToList();

        var grantedPermissionNames = await roleManager.GetGrantedPermissionNamesAsync(roleNames);
        var grantedPermissions = this.PermissionManager.GetPermissionsFromNamesByValidating(grantedPermissionNames);

        if (roleNames.Any())
        {
            this.CheckErrors(await userManager.AddToRolesAsync(user, roleNames));

            await userManager.SetGrantedPermissionsAsync(user, grantedPermissions);

            await cachingAppService.ClearPermissionsCache();
        }

        return user;
    }

    private async Task CheckUniqueEmailForTenant(int? tenantId, UserCreateRequestModel userModel)
    {
        var userExists = await this.UserManager.Users.IgnoreQueryFilters().AnyAsync(u =>
            u.EmailAddress.ToLower() == userModel.EmailAddress.ToLower() && u.TenantId == tenantId);

        if (userExists)
        {
            throw new UserFriendlyException(this.L("EmailAddressIsAlreadyRegistered"));
        }
    }

    private async Task<UserAccount> CreateUserAccountAsync(int? tenantId, User user)
    {
        var userAccount = new UserAccount
        {
            UserId = user.Id,
            UserName = user.UserName,
            EmailAddress = user.EmailAddress,
            TenantId = tenantId,
            IsDeleted = !user.IsActive,
        };

        await this.Db.AddAsync(userAccount);

        return userAccount;
    }

    private async Task<Employee?> CreateEmployeeInternal(
        int? tenantId,
        EmployeeRequestModel employeeModel,
        UserAccount userAccount)
    {
        var isRootAdmin = tenantId is null;

        if (isRootAdmin)
        {
            return null;
        }

        var employee = this.ObjectMapper.Map<Employee>(employeeModel);

        employee.UserAccount = userAccount;

        await this.Db.AddAsync(employee);

        return employee;
    }

    private async Task<User> GetByIdAsync(long id)
        => await this.UserManager
            .Users
            .IgnoreQueryFilters(this.IgnoreQueryFilters)
            .Where(u => u.Id == id)
            .FirstAsync();

    private bool IsGlobalAdministrator(User user)
        => user.UserName == AbpUserBase.AdminUserName && user.TenantId is null;
}