namespace RealtoCrm.Authorization.Roles;

using System.Collections.Generic;
using System.Threading.Tasks;
using Models;

/// <summary>
/// Application service that is used by 'role management' page.
/// </summary>
public interface IRoleAppService : IDataCrudAppService<
    int,
    Role,
    RoleRequestModel,
    RoleRequestModel,
    RolesPaginatedRequestModel,
    RoleDetailsResponseModel,
    RoleResponseModel>
{
    Task CreateInitialRolesForTenantAsync(int tenantId);

    Task<IEnumerable<RoleResponseModel>> GetAllByUserId(long userId);

    Task<IEnumerable<ManagerUserRoleResponseModel>> GetManagersByTenantAsync(int tenantId);

    Task<IEnumerable<string>> GetGrantedPermissionNamesAsync(RoleGrantedPermissionsRequestModel request);
}