namespace RealtoCrm.Authorization.Roles.Models;

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AutoMapper;
using Mapping;

public class RoleRequestModel : IMapTo<Role>, IMapExplicitly
{
    [Required]
    public string DisplayName { get; init; } = default!;

    public bool IsDefault { get; init; } = default!;

    public int? TenantId { get; init; }

    [Required]
    public IEnumerable<string> GrantedPermissionNames { get; init; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<RoleRequestModel, Role>()
            .ForMember(m => m.Name, cfg => cfg
                .MapFrom(m => m.DisplayName));
}