namespace RealtoCrm.Estates.Models;

using Mapping;

public class EstateDetailRequestModel : IMapTo<EstateDetail>
{
    public double? KINTArea { get; init; }
    
    public double? UsefulFloorArea { get; init; }
    
    public double? DensityArea { get; init; }
    
    public double? PossibleArea { get; init; }
    
    public double? BuildupArea { get; init; }
    
    public double? PlotArea { get; init; }
    
    public int? HouseTypeId { get; init; }
    
    public int? InfrastructureId { get; init; }
    
    public int? ConstructionPurposeId { get; init; }
    
    public int? FenceId { get; init; }
    
    public int? BuildingPurposeId { get; init; }
}