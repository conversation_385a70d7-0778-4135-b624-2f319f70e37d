namespace RealtoCrm.Employees.Specifications;

using System;
using System.Linq.Expressions;

public class EmployeeByIdSpecification(string? id) : Specification<Employee>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(id);

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        var canBeParsed = int.TryParse(id, out var parsedId);

        if (!canBeParsed)
        {
            return employee => false;
        }

        return employee => employee.Id == parsedId;
    }
}