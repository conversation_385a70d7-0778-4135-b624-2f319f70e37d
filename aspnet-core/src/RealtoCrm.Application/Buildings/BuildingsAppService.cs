namespace RealtoCrm.Buildings;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Common.Attributes;
using EntityFrameworkCore;
using Models;
using Expressions;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Specifications;

public class BuildingsAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Building,
        BuildingRequestModel,
        BuildingRequestModel,
        BuildingsPaginatedRequestModel,
        BuildingResponseModel,
        BuildingResponseModel>(dbContextProvider, expressionsBuilder), IBuildingsAppService
{
    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.BulkAddOrUpdateAsync))
            .AddCustomAttribute(nameof(this.BulkAddOrUpdateAsync), new RequireTenantAttribute());

    protected override Specification<Building> GetSpecification(BuildingsPaginatedRequestModel request)
        => new BuildingByNameSpecification(request.Name)
            .And(new BuildingByEstateGroupSpecification(request.EstateGroupId));

    public async Task BulkAddOrUpdateAsync(BuildingBulkAddOrUpdateRequestModel request)
    {
        var tenantId = this.GetTenantId();

        var buildingsToRemove = await this.Data
            .Buildings
            .Where(b => request.BuildingIdsToRemove.Contains(b.Id))
            .ToListAsync();

        this.Data.Buildings.RemoveRange(buildingsToRemove);

        request.Buildings.ForEach(b => b.TenantId = tenantId);

        var buildings = this.ObjectMapper.Map<IEnumerable<Building>>(request.Buildings);

        this.Data.UpdateRange(buildings);

        await this.Data.SaveChangesAsync();
    }

    public async Task<IEnumerable<BuildingIdAndNameResponseModel>> GetByNamesAsync(IEnumerable<string> names)
        => await this.ObjectMapper
            .ProjectTo<BuildingIdAndNameResponseModel>(this
                .AllAsNoTracking()
                .Where(b => names
                    .Any(n => n.ToLower() == b.Name.ToLower())))
            .ToListAsync();
}