namespace RealtoCrm;

using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Domain.Entities.Auditing;
using DataCrudModels;
using Mapping;

public interface IDataCrudAppService<
    TPrimaryKey,
    TEntity,
    in TCreateRequestModel,
    in TUpdateRequestModel,
    in TPaginatedRequestModel,
    TDetailsResponseModel,
    TListingResponseModel> : IApplicationService
    where TPrimaryKey : struct
    where TEntity : FullAuditedEntity<TPrimaryKey>
    where TCreateRequestModel : class, IMapTo<TEntity>
    where TUpdateRequestModel : class, IMapTo<TEntity>
    where TPaginatedRequestModel : PaginatedRequestModel
    where TDetailsResponseModel : class, IMapFrom<TEntity>
    where TListingResponseModel : class, IMapFrom<TEntity>
{
    Task<TDetailsResponseModel?> GetDetailsAsync(TPrimaryKey id);

    Task<PaginatedResponseModel<TListingResponseModel>> GetAllAsync(TPaginatedRequestModel request);

    Task<TPrimaryKey> CreateAsync(TCreateRequestModel request);

    Task<TPrimaryKey> UpdateAsync(TPrimaryKey id, TUpdateRequestModel request);

    Task<bool> DeleteAsync(TPrimaryKey id);
}