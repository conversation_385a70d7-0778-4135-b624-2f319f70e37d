<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <AssemblyName>RealtoCrm.Application</AssemblyName>
    <PackageId>RealtoCrm.Application</PackageId>
    <GenerateAssemblyTitleAttribute>false</GenerateAssemblyTitleAttribute>
    <GenerateAssemblyDescriptionAttribute>false</GenerateAssemblyDescriptionAttribute>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <RootNamespace>RealtoCrm</RootNamespace>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Abp.EntityFrameworkCore.EFPlus" Version="9.0.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.2.0" />
    <PackageReference Include="MiniExcel" Version="1.31.2" />
    <PackageReference Include="NodaTime" Version="3.2.2" />
    <PackageReference Include="PuppeteerSharp" Version="20.1.3" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.6" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RealtoCrm.Application.Shared\RealtoCrm.Application.Shared.csproj" />
    <ProjectReference Include="..\RealtoCrm.Core\RealtoCrm.Core.csproj" />
    <ProjectReference Include="..\RealtoCrm.EntityFrameworkCore\RealtoCrm.EntityFrameworkCore.csproj" />
  </ItemGroup>
</Project>