namespace RealtoCrm.Nomenclatures.Countries.Models;

using System.ComponentModel.DataAnnotations;
using Common.Models;
using Mapping;
using static ModelConstants.Address;

public class CountryRequestModel : NomenclatureRequestModel, IMapTo<Country>
{
    [Required]
    [MaxLength(MaxCodeAlpha2Length)]
    public string CodeAlpha2 { get; init; } = default!;

    [Required]
    [MaxLength(MaxCodeAlpha3Length)]
    public string CodeAlpha3 { get; init; } = default!;
}