namespace RealtoCrm.Nomenclatures.ExternalAgencies;

using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;

public class ExternalAgenciesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : NomenclaturesAppService<
        int,
        ExternalAgency,
        NomenclatureRequestModel,
        NomenclatureRequestModel,
        NomenclaturesPaginatedRequestModel,
        NomenclatureResponseModel,
        NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IExternalAgenciesAppService;
