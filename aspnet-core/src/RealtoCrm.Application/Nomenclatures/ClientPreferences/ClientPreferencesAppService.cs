namespace RealtoCrm.Nomenclatures.ClientPreferences;

using Abp.EntityFrameworkCore;
using RealtoCrm.DataExporting;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Expressions;
using RealtoCrm.Nomenclatures.Common;
using RealtoCrm.Nomenclatures.Common.Models;

public class ClientPreferencesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    ClientPreference,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IClientPreferencesAppService;