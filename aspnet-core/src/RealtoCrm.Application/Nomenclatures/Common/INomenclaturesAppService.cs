namespace RealtoCrm.Nomenclatures.Common;

using System.Threading.Tasks;
using DataExporting.Models;
using Mapping;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Models;

public interface INomenclaturesAppService<
    TPrimaryKey,
    TEntity,
    in TCreateRequestModel,
    in TUpdateRequestModel,
    in TPaginatedRequestModel,
    TDetailsResponseModel,
    TListingResponseModel>
    : IDataCrudAppService<
        TPrimaryKey,
        TEntity,
        TCreateRequestModel,
        TUpdateRequestModel,
        TPaginatedRequestModel,
        TDetailsResponseModel,
        TListingResponseModel>
    where TPrimaryKey : struct
    where TEntity : Nomenclature<TPrimaryKey>, new()
    where TCreateRequestModel : NomenclatureRequestModel, IMapTo<TEntity>, new()
    where TUpdateRequestModel : NomenclatureRequestModel, IMapTo<TEntity>
    where TPaginatedRequestModel : NomenclaturesPaginatedRequestModel
    where TDetailsResponseModel : NomenclatureResponseModel, IMapFrom<TEntity>
    where TListingResponseModel : NomenclatureResponseModel, IMapFrom<TEntity>
{
    Task<TPrimaryKey> GetIdByNameAsync(string name);

    Task ImportExcelAsync(IFormFile file);

    Task<FileContentResult> ExportExcelAsync(ExportType exportType);
}