namespace RealtoCrm.Nomenclatures.Vats;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;
using Microsoft.EntityFrameworkCore;

public class VatsAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : NomenclaturesAppService<
        int,
        Vat,
        NomenclatureRequestModel,
        NomenclatureRequestModel,
        NomenclaturesPaginatedRequestModel,
        NomenclatureResponseModel,
        NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IVatsAppService
{
    public async Task<IEnumerable<NomenclatureResponseModel>> GetByNamesAsync(IEnumerable<string> names)
        => await this.ObjectMapper
            .ProjectTo<NomenclatureResponseModel>(this
                .AllAsNoTracking()
                .Where(v => names
                    .Any(n => n.ToLower() == v.Name.ToLower())))
            .ToListAsync();
}