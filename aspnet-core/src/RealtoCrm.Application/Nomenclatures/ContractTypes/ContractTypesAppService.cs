namespace RealtoCrm.Nomenclatures.ContractTypes;

using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;
using Microsoft.EntityFrameworkCore;
using static CosherConsts.ContractTypes;

public class ContractTypesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    ContractType,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IContractTypesAppService
{

    public async Task<IEnumerable<NomenclatureResponseModel>> GetBasicTypesAsync(IEnumerable<string> contractTypeNames)
     => await this.ObjectMapper.ProjectTo<NomenclatureResponseModel>(
             this.AllAsNoTracking()
                 .Where(ct => contractTypeNames.Contains(ct.Name)))
         .ToListAsync();
}