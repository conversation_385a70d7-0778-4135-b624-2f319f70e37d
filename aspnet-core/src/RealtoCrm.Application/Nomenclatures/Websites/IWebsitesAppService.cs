namespace RealtoCrm.Nomenclatures.Websites;

using Common;
using Common.Models;
using Models;
using System.Collections.Generic;
using System.Threading.Tasks;


public interface IWebsitesAppService : INomenclaturesAppService<
    int,
    Website,
    WebsiteRequestModel,
    WebsiteRequestModel,
    NomenclaturesPaginatedRequestModel,
    WebsiteResponseModel,
    WebsiteResponseModel>
{
    Task<IEnumerable<WebsiteResponseModel>> GetAllWithoutPagination();
}