namespace RealtoCrm.Nomenclatures.DealMotives;

using Abp.EntityFrameworkCore;
using Common;
using Common.Models;
using DataExporting;
using EntityFrameworkCore;
using Expressions;

public class DealMotivesAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    DealMotive,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IDealMotivesAppService;