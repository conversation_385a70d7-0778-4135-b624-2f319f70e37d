namespace RealtoCrm.Nomenclatures.JobPositions;

using Abp.EntityFrameworkCore;
using DataExporting;
using EntityFrameworkCore;
using Expressions;
using Common;
using Common.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

public class JobPositionsAppService(
    IExcelService excelService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder) : NomenclaturesAppService<
    int,
    JobPosition,
    NomenclatureRequestModel,
    NomenclatureRequestModel,
    NomenclaturesPaginatedRequestModel,
    NomenclatureResponseModel,
    NomenclatureResponseModel>(excelService, dbContextProvider, expressionsBuilder), IJobPositionsAppService
{
    public async Task<IEnumerable<NomenclatureResponseModel>> GetAllWithoutPagination()
        => await this.ObjectMapper
            .ProjectTo<NomenclatureResponseModel>(this
                .AllAsNoTracking())
            .ToListAsync();
}