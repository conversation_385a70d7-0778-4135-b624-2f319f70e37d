namespace RealtoCrm.PropertyToEstateCategorySettings.Specifications;

using System;
using System.Linq.Expressions;

public class PropertyToEstateCategorySettingByTenantIdSpecification : Specification<PropertyToEstateCategorySetting>
{
    private readonly int tenantId;

    public PropertyToEstateCategorySettingByTenantIdSpecification(int tenantId) => this.tenantId = tenantId;

    protected override bool Include => this.tenantId > 0;

    public override Expression<Func<PropertyToEstateCategorySetting, bool>> ToExpression()
        => propertyToEstateCategorySetting => propertyToEstateCategorySetting.TenantId == this.tenantId;
}