namespace RealtoCrm.PropertyToEstateCategorySettings;

using Abp.EntityFrameworkCore;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Expressions;
using RealtoCrm.PropertyToEstateCategorySettings.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Runtime.Session;
using Microsoft.EntityFrameworkCore;
using Abp.UI;
using RealtoCrm.PropertyToEstateCategorySettings.Specifications;

public class PropertyToEstateCategorySettingAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
            int,
            PropertyToEstateCategorySetting,
            PropertyToEstateCategorySettingCreateRequestModel,
            PropertyToEstateCategorySettingUpdateRequestModel,
            PropertyToEstateCategorySettingsPaginatedRequestModel,
            PropertyToEstateCategorySettingResponseModel,
            PropertyToEstateCategorySettingResponseModel>(dbContextProvider, expressionsBuilder),
        IPropertyToEstateCategorySettingAppService
{
    protected override Specification<PropertyToEstateCategorySetting> GetSpecification(
        PropertyToEstateCategorySettingsPaginatedRequestModel request)
        => new PropertyToEstateCategorySettingByTenantIdSpecification(request.TenantId);

    public async Task<List<string>> GetPropertyNamesAsync()
        => await this.Data.PropertyToEstateCategorySettings
            .AsNoTracking()
            .OrderBy(x => x.PropertyName)
            .Select(x => x.PropertyName)
            .Distinct()
            .ToListAsync();

    public async Task<ICollection<PropertyToEstateCategorySettingResponseModel>>
        GetPropertiesByEstateCategoryAndTenantIdAsync(int categoryId)
        => await this.ObjectMapper.ProjectTo<PropertyToEstateCategorySettingResponseModel>(this.AllAsNoTracking()
            .Where(pbec
                => pbec.TenantId == this.AbpSession.GetTenantId() && pbec.CategoryId == categoryId)).ToListAsync();


    public override async Task<int> UpdateAsync(int id, PropertyToEstateCategorySettingUpdateRequestModel request)
    {
        var entities = await this.Data.PropertyToEstateCategorySettings
            .Where(x => x.TenantId == request.TenantId)
            .ToListAsync();

        entities.ForEach(entity =>
        {
            var correspondingModel = request.PropertyEstate.FirstOrDefault(x
                => x.Id == entity.Id);

            if (correspondingModel is null)
            {
                throw new UserFriendlyException($"Property with name '{entity.PropertyName}' not found.");
            }

            this.ObjectMapper.Map(correspondingModel, entity);
        });

        this.Data.UpdateRange(entities);
        return await this.Data.SaveChangesAsync();
    }
}