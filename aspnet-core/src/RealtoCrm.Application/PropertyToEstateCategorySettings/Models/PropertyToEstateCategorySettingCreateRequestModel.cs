namespace RealtoCrm.PropertyToEstateCategorySettings.Models;

using RealtoCrm.Mapping;

public class PropertyToEstateCategorySettingCreateRequestModel : IMapTo<PropertyToEstateCategorySetting>
{
    public string PropertyName { get; set; } = default!;

    public PropertyToEstateCategoryModel Model { get; set; }

    public int CategoryId { get; set; } = default!;

    public bool IsVisible { get; set; }

    public int TenantId { get; set; }
}