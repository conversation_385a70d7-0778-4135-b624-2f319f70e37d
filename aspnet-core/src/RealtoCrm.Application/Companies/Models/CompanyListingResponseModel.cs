namespace RealtoCrm.Companies.Models;

using System.Linq;
using AutoMapper;
using Mapping;

public class CompanyListingResponseModel : IMapFrom<Company>, IMapExplicitly
{
    public int Id { get; init; }

    public string Name { get; init; } = default!;

    public int TenantId { get; init; }

    public bool IsActive { get; init; }

    public int TotalDivisions { get; init; }

    public int ActiveDivisions { get; init; }

    public int InactiveDivisions { get; init; }

    public int TotalEmployees { get; set; }

    public int ActiveEmployees { get; set; }

    public int InactiveEmployees { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Company, CompanyListingResponseModel>()
            .ForMember(m => m.TotalDivisions, cfg => cfg
                .MapFrom(m => m.Divisions.Count))
            .ForMember(m => m.ActiveDivisions, cfg => cfg
                .MapFrom(m => m.Divisions.Count(d => d.IsActive)))
            .ForMember(m => m.InactiveDivisions, cfg => cfg
                .MapFrom(m => m.Divisions.Count(d => !d.IsActive)));
}