namespace RealtoCrm.Companies.Models;

using Addresses.Models;
using AutoMapper;
using Mapping;

public class CompanyDetailsResponseModel : IMapFrom<Company>, IMapExplicitly
{
    public int Id { get; init; }

    public string Name { get; init; } = default!;

    public int TenantId { get; init; }

    public string? Bulstat { get; init; }

    public string? MaterialResponsiblePerson { get; init; }

    public string? LogoUrl { get; init; }

    public string? SmallLogoUrl { get; init; }

    public bool IsActive { get; init; }

    public AddressResponseModel? RegistrationAddress { get; init; }


    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Company, CompanyDetailsResponseModel>()
            .ForMember(m => m.LogoUrl, cfg => cfg
                .MapFrom(m => m.Tenant.LogoUrl))
            .ForMember(m => m.SmallLogoUrl, cfg => cfg
                .MapFrom(m => m.Tenant.SmallLogoUrl));
}