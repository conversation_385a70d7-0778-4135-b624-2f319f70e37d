namespace RealtoCrm.Companies;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using DataCrudModels;
using Employees.Models;
using EntityFrameworkCore;
using Models;
using Expressions;
using Extensions;
using Microsoft.EntityFrameworkCore;
using MultiTenancy;
using MultiTenancy.Models;
using RealtoCrm.Mapping;
using Specifications;
using static CosherConsts.Tenants;

public class CompaniesAppService(
    ITenantAppService tenantsAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Company,
        TenantCreateRequestModel,
        TenantUpdateRequestModel,
        CompaniesPaginatedRequestModel,
        CompanyDetailsResponseModel,
        CompanyListingResponseModel>(dbContextProvider, expressionsBuilder), ICompaniesAppService
{
    private static readonly string[] CompaniesForLinkedProjects =
    [
        AddressTenantName,
        ImotekaTenantName,
        UniqueEstatesTenantName,
        NewEstatesTenantName,
        CWFortonTenantName,
    ];

    public async Task<TResponseModel> GetCurrentCompanyDetails<TResponseModel>()
        where TResponseModel : IMapFrom<Company>
        => await this.ObjectMapper
            .ProjectTo<TResponseModel>(this
                .AllAsNoTracking()
                .Where(c => c.TenantId == this.AbpSession.TenantId))
            .SingleAsync();
    
    public async Task<string> GetCompanyNameByTenantAsync(int tenantId)
        => await this
            .AllAsNoTracking()
            .Where(c => c.TenantId == tenantId)
            .Select(c => c.Name)
            .SingleAsync();

    public async Task<Tenant?> GetTenantByIdAsync(int? tenantId)
        => await this
            .All()
            .Where(c => c.Id == tenantId)
            .Select(c => c.Tenant)
            .SingleOrDefaultAsync();

    public async Task<IEnumerable<CompanyByTenantsResponseModel>> GetCompaniesByTenantsAsync(
        IEnumerable<int?> tenantIds)
        => await this.ObjectMapper
            .ProjectTo<CompanyByTenantsResponseModel>(this
                .AllAsNoTracking()
                .Where(c => tenantIds.Contains(c.TenantId)))
            .ToListAsync();

    public async Task<IEnumerable<CompanyDropdownResponseModel>> GetNameAndTenantIdAsync()
        => await this.ObjectMapper
            .ProjectTo<CompanyDropdownResponseModel>(this
                .AllAsNoTracking())
            .ToListAsync();

    public async Task<IEnumerable<CompanyForLinkedProjectResponseModel>> GetAllForLinkedProjectAsync()
        => await this.ObjectMapper
            .ProjectTo<CompanyForLinkedProjectResponseModel>(this
                .AllAsNoTracking()
                .Where(c => CompaniesForLinkedProjects.Contains(c.Name)))
            .ToListAsync();

    public override async Task<int> CreateAsync(TenantCreateRequestModel tenantModel)
        => await tenantsAppService.CreateAsync(tenantModel);

    public override async Task<int> UpdateAsync(int id, TenantUpdateRequestModel request)
        => await tenantsAppService.UpdateAsync(id, request);

    public override async Task<bool> DeleteAsync(int id)
        => await tenantsAppService.DeleteAsync(id);

    protected override Specification<Company> GetSpecification(CompaniesPaginatedRequestModel request)
        => new CompanyByNameSpecification(request.Name)
            .And(new CompanyOnlyActiveSpecification(request.OnlyActive));


    public async Task<IEnumerable<CompanyForFiltering>> GetForFilteringAsync()
        => await this.ObjectMapper
            .ProjectTo<CompanyForFiltering>(this
                .AllAsNoTracking()
                .IgnoreQueryFilters()
                .Where(c => c.IsActive))
            .ToListAsync();

    protected override async Task<IEnumerable<CompanyListingResponseModel>> GetListingAsync(
        Specification<Company> specification,
        Expression<Func<Company, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<Company>> sortByExpressions,
        int skip = 0,
        int take = int.MaxValue)
    {
        var companies = await base.GetListingAsync(specification, filtersExpression, sortByExpressions, skip, take)
            .ToListAsync();

        var tenantIds = companies
            .Select(c => (int?)c.TenantId)
            .ToList();

        var employees = await this.GetEmployeeUserAccountsByTenantsAsync(tenantIds);

        foreach (var company in companies)
        {
            var companyEmployees = employees
                .Where(e => e.TenantId == company.TenantId)
                .ToList();

            company.TotalEmployees = companyEmployees.Count;
            company.ActiveEmployees = companyEmployees.Count(e => !e.IsDeleted);
            company.InactiveEmployees = companyEmployees.Count(e => e.IsDeleted);
        }

        return companies;
    }

    private async Task<IEnumerable<EmployeeUserAccountResponseModel>> GetEmployeeUserAccountsByTenantsAsync(
        IEnumerable<int?> tenantIds)
        => await this.ObjectMapper
            .ProjectTo<EmployeeUserAccountResponseModel>(this
                .Data
                .Employees
                .AsNoTracking()
                .IgnoreQueryFilters()
                .Where(e => tenantIds.Contains(e.UserAccount.TenantId)))
            .ToListAsync();
}