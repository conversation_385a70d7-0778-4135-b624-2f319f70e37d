namespace RealtoCrm.Companies.Offices.Specifications;

using System;
using System.Linq.Expressions;

public class OfficeOnlyActiveSpecification : Specification<Office>
{
    private readonly bool onlyActive;

    public OfficeOnlyActiveSpecification(bool onlyActive)
        => this.onlyActive = onlyActive;

    public override Expression<Func<Office, bool>> ToExpression()
    {
        if (this.onlyActive)
        {
            return office => office.IsActive;
        }

        return office => true;
    }
}