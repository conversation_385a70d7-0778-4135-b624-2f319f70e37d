namespace RealtoCrm.Companies.Teams.Models;

using System.Linq;
using AutoMapper;
using Mapping;

public class TeamListingResponseModel : TeamDetailsResponseModel, IMapExplicitly
{
    public int TotalEmployees { get; init; }

    public int ActiveEmployees { get; init; }

    public int InactiveEmployees { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Team, TeamListingResponseModel>()
            .ForMember(m => m.TotalEmployees, cfg => cfg
                .MapFrom(m => m.Employees.Count))
            .ForMember(m => m.ActiveEmployees, cfg => cfg
                .MapFrom(m => m.Employees.Count(e => !e.UserAccount.IsDeleted)))
            .ForMember(m => m.InactiveEmployees, cfg => cfg
                .MapFrom(m => m.Employees.Count(e => e.UserAccount.IsDeleted)));
}