namespace RealtoCrm.Companies.Teams.Models;

using System.ComponentModel.DataAnnotations;
using Mapping;
using static RealtoCrm.ModelConstants.Common;

public class TeamRequestModel : IMapTo<Team>
{
    [Required]
    [MinLength(MinNameLength)]
    [MaxLength(MaxNameLength)]
    public string Name { get; init; } = default!;

    public int? ParentId { get; init; }

    public int TenantId { get; init; }

    public int CompanyId { get; init; }

    public int? DepartmentId { get; init; }

    public bool IsActive { get; init; }
}