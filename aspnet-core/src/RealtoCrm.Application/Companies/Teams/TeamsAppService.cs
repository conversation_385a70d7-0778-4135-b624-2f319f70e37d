namespace RealtoCrm.Companies.Teams;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Common.Extensions;
using DataCrudModels;
using EntityFrameworkCore;
using Models;
using Expressions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Specifications;

public class TeamsAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Team,
        TeamRequestModel,
        TeamRequestModel,
        TeamsPaginatedRequestModel,
        TeamDetailsResponseModel,
        TeamListingResponseModel>(dbContextProvider, expressionsBuilder), ITeamsAppService
{
    protected override Dictionary<string, FilterExpression> CustomFilters
        => new()
        {
            [nameof(TeamListingResponseModel.ParentName)]
                = new FilterExpression(
                    typeof(Team).GetProperty(nameof(Team.Name))!,
                    Expression.PropertyOrField(
                        Expression.PropertyOrField(this.Parameter, nameof(Team.Parent)), nameof(Team.Name))),
            [nameof(TeamListingResponseModel.DepartmentName)]
                = new FilterExpression(
                    typeof(Department).GetProperty(nameof(Department.Name))!,
                    Expression.PropertyOrField(
                        Expression.PropertyOrField(this.Parameter, nameof(Team.Department)), nameof(Department.Name))),
            [nameof(TeamListingResponseModel.CompanyName)]
                = expressionsBuilder.BuildForCompanyName<Team>(this.Parameter)
        };

    protected override Specification<Team> GetSpecification(TeamsPaginatedRequestModel request)
        => new TeamByNameSpecification(request.Name)
            .And(new TeamByParentSpecification(request.ParentId))
            .And(new TeamByDepartmentSpecification(request.DepartmentId))
            .And(new TeamByCompanyTenantSpecification(request.TenantId))
            .And(new TeamOnlyActiveSpecification(request.OnlyActive));

    [HttpPost]
    public async Task<PaginatedResponseModel<TeamEmployeeResponseModel>> GetAllTeamEmployeesAsync(
        TeamEmployeePaginatedRequestModel request)
    {
        var specification = this.GetTeamEmployeesSpecification(request);

        var skip = request.Page * request.PageSize;

        var filtersExpression = await this.BuildDataCrudFiltersExpression(request.Filters);

        var sorterExpressionDefinitions = await this.BuildDataCrudSortByExpression(request);

        var items = await this.GetTeamEmployeesListingAsync(
            specification,
            filtersExpression,
            sorterExpressionDefinitions,
            skip,
            take: request.PageSize);

        var total = await this.GetTotalTeamEmployees(specification, filtersExpression);

        var totalPages = (int)Math.Ceiling((double)total / request.PageSize);

        return new PaginatedResponseModel<TeamEmployeeResponseModel>(items, request.Page, total, totalPages);
    }

    // IgnoreQueryFilters, because Employee IsActive = !IsDeleted
    protected override IQueryable<Team> GetEntityQuery(
        Specification<Team> specification,
        Expression<Func<Team, bool>> filtersExpression)
        => base
            .GetEntityQuery(specification, filtersExpression)
            .IgnoreQueryFilters()
            .Where(t => !t.IsDeleted);

    private async Task<IEnumerable<TeamEmployeeResponseModel>> GetTeamEmployeesListingAsync(
        Specification<Team> specification,
        Expression<Func<Team, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<Team>> sortByExpressions,
        int skip = 0,
        int take = int.MaxValue)
        => await this.ObjectMapper
            .ProjectTo<TeamEmployeeResponseModel>(this
                .AllAsNoTracking()
                .IgnoreQueryFilters(this.IgnoreQueryFilters)
                .Where(filtersExpression)
                .Where(specification)
                .ApplySortByDefinitions(sortByExpressions)
                .SelectMany(t => t.Employees)
                .Skip(skip)
                .Take(take))
            .ToListAsync();

    private async Task<int> GetTotalTeamEmployees(
        Specification<Team> specification,
        Expression<Func<Team, bool>> filtersExpression)
        => await this
            .AllAsNoTracking()
            .IgnoreQueryFilters(this.IgnoreQueryFilters)
            .Where(filtersExpression)
            .Where(specification)
            .SelectMany(t => t.Employees)
            .CountAsync();

    private Specification<Team> GetTeamEmployeesSpecification(TeamEmployeePaginatedRequestModel request)
        => new TeamByIdSpecification(request.Id)
            .And(new TeamByDepartmentSpecification(request.DepartmentId));
}