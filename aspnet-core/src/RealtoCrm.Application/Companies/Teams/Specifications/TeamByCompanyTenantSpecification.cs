namespace RealtoCrm.Companies.Teams.Specifications;

using System;
using System.Linq.Expressions;

public class TeamByCompanyTenantSpecification : Specification<Team>
{
    private readonly int? tenantId;

    public TeamByCompanyTenantSpecification(int? tenantId) => this.tenantId = tenantId;

    protected override bool Include => this.tenantId is not null;

    public override Expression<Func<Team, bool>> ToExpression()
        => team => team.Company.TenantId == this.tenantId;
}