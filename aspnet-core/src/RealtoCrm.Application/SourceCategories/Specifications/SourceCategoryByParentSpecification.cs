namespace RealtoCrm.SourceCategories.Specifications;

using System;
using System.Linq.Expressions;

public class SourceCategoryByParentSpecification : Specification<SourceCategory>
{
    private readonly int? parentId;

    public SourceCategoryByParentSpecification(int? parentId) => this.parentId = parentId;

    public override Expression<Func<SourceCategory, bool>> ToExpression()
        => sourceCategory => sourceCategory.ParentId == this.parentId;
}