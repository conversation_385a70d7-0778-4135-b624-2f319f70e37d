namespace RealtoCrm.BlobStorage;

using System;
using System.IO;
using System.Threading.Tasks;
using Abp.Dependency;
using Azure.Storage.Blobs.Models;

public interface IBlobStorageService : ITransientDependency
{
    /// <summary>
    /// Uploads a file to Azure Blob Storage.
    /// </summary>
    /// <param name="fileStream">File stream.</param>
    /// <param name="blobName">Blob name.</param>
    /// <param name="containerName">Container name.</param>
    /// <param name="blobHttpHeaders">Http headers</param>
    /// <returns>Blob Uri</returns>
    /// <exception cref="InvalidOperationException">Throws exception if Azure Blob Storage connection string is not configured.</exception>
    Task<string> UploadFileAsync(
        Stream fileStream,
        string blobName,
        string containerName,
        BlobHttpHeaders? blobHttpHeaders = null);

    /// <summary>
    /// Checks is a file already exists in the storage.
    /// </summary>
    /// <param name="blobName">Blob name.</param>
    /// <param name="containerName">Container name.</param>
    /// <exception cref="InvalidOperationException">Throws exception if Azure Blob Storage connection string is not configured.</exception>
    Task<bool> BlobExistsAsync(string containerName, string blobName);

    Task<byte[]> DownloadBlobAsync(string containerName, string blobName);
}