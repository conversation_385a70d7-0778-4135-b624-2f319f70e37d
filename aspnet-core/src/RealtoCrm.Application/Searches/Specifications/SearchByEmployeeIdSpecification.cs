namespace RealtoCrm.Searches.Specifications;

using System;
using System.Linq.Expressions;

public class SearchByEmployeeIdSpecification : Specification<Search>
{
    private readonly int? employeeId;

    public SearchByEmployeeIdSpecification(int? employeeId) => this.employeeId = employeeId;

    protected override bool Include => this.employeeId is not null;

    public override Expression<Func<Search, bool>> ToExpression()
        => search => search.EmployeeId == this.employeeId;
}