namespace RealtoCrm.Searches.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;

public class SearchByDistrictIdsSpecification(int[]? districtIds)
    : Specification<Search>
{
    protected override bool Include => districtIds != null && districtIds.Length > 0;

    public override Expression<Func<Search, bool>> ToExpression()
    {
        return search => search.SearchesDistricts
            .Any(sc => districtIds!.Contains(sc.DistrictId));
    }
}