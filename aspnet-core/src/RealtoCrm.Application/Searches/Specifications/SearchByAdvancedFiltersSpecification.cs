namespace RealtoCrm.Searches.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;
using Expressions;
using Models;

public class SearchByAdvancedFiltersSpecification(SearchAdvancedFiltersFilteringModel? filters)
    : Specification<Search>
{
    protected override bool Include => filters is not null;

    public override Expression<Func<Search, bool>> ToExpression() =>
        ExpressionsHelper.And(
            FilterByBedroomsRange(),
            FilterByBathroomsRange(),
            FilterByRoomsRange(),
            FilterByTerracesRange(),
            FilterByDepartment(),
            FilterByConstructionTypeIds(),
            FilterByCompletionLevelIds(),
            FilterByLifestyleIds(),
            FilterByConditionIds(),
            FilterByHasDeposits(),
            FilterByHasViewings(),
            FilterByHasDeal(),
            FilterByContractDateRange(),
            FilterByArchiveDateRange(),
            FilterByFurnitureIds(),
            FilterByEstateGroupIds()
        );

    private Expression<Func<Search, bool>> FilterByBedroomsRange() =>
        search => (filters.BedroomsFrom == null || search.BedroomsFrom >= filters.BedroomsFrom) &&
                  (filters.BedroomsTo == null || search.BedroomsTo <= filters.BedroomsTo);

    private Expression<Func<Search, bool>> FilterByBathroomsRange() =>
        search => (filters.BathroomsFrom == null || search.BathroomsFrom >= filters.BathroomsFrom) &&
                  (filters.BathroomsTo == null || search.BathroomsTo <= filters.BathroomsTo);

    private Expression<Func<Search, bool>> FilterByRoomsRange() =>
        search => (filters.RoomsFrom == null || search.RoomsFrom >= filters.RoomsFrom) &&
                  (filters.RoomsTo == null || search.RoomsTo <= filters.RoomsTo);

    private Expression<Func<Search, bool>> FilterByTerracesRange() =>
        search => (filters.TerracesCountFrom == null || search.TerracesCount >= filters.TerracesCountFrom) &&
                  (filters.TerracesCountTo == null || search.TerracesCount <= filters.TerracesCountTo);

    private Expression<Func<Search, bool>> FilterByDepartment() =>
        search => filters.DepartmentId == null ||
                  (search.Employee != null && search.Employee.DepartmentId == filters.DepartmentId);

    private Expression<Func<Search, bool>> FilterByConstructionTypeIds() =>
        search => filters.ConstructionTypeIds == null || filters.ConstructionTypeIds.Length == 0 ||
                  search.SearchesConstructionTypes.Any(c => filters.ConstructionTypeIds.Contains(c.ConstructionTypeId));

    private Expression<Func<Search, bool>> FilterByCompletionLevelIds() =>
        search => filters.CompletionLevelIds == null || filters.CompletionLevelIds.Length == 0 ||
                  search.SearchesCompletionLevels.Any(c => filters.CompletionLevelIds.Contains(c.CompletionLevelId));

    private Expression<Func<Search, bool>> FilterByLifestyleIds() =>
        search => filters.LifestyleIds == null || filters.LifestyleIds.Length == 0 ||
                  search.SearchesLifestyles.Any(l => filters.LifestyleIds.Contains(l.LifestyleId));

    private Expression<Func<Search, bool>> FilterByConditionIds() =>
        search => filters.ConditionIds == null || filters.ConditionIds.Length == 0 ||
                  search.SearchesConditions.Any(c => filters.ConditionIds.Contains(c.ConditionId));

    private Expression<Func<Search, bool>> FilterByHasDeposits() =>
        search => filters.HasDeposits == null ||
                  (filters.HasDeposits == true && search.Deposits.Any()) ||
                  (filters.HasDeposits == false && !search.Deposits.Any());

    private Expression<Func<Search, bool>> FilterByHasViewings() =>
        search => filters.HasViewings == null ||
                  (filters.HasViewings == true && search.Viewings.Any()) ||
                  (filters.HasViewings == false && !search.Viewings.Any());

    private Expression<Func<Search, bool>> FilterByHasDeal() =>
        search => filters.HasDeal == null ||
                  (filters.HasDeal == true && search.Deals.Any()) ||
                  (filters.HasDeal == false && !search.Deals.Any());

    private Expression<Func<Search, bool>> FilterByContractDateRange() =>
        search => (filters.ContractDateFrom == null && filters.ContractDateTo == null) ||
                  search.Contracts.Any(c =>
                      c.SignDate != null &&
                      (filters.ContractDateFrom == null || 
                       (filters.ContractDateTo == null 
                           ? c.SignDate >= filters.ContractDateFrom && c.SignDate < filters.ContractDateFrom.Value.AddDays(1)
                           : c.SignDate >= filters.ContractDateFrom && c.SignDate <= filters.ContractDateTo)));

    private Expression<Func<Search, bool>> FilterByArchiveDateRange() =>
        search => filters.ArchiveDateFrom == null || 
                  (filters.ArchiveDateTo == null 
                      ? search.ArchiveDate >= filters.ArchiveDateFrom && search.ArchiveDate < filters.ArchiveDateFrom.Value.AddDays(1)
                      : search.ArchiveDate >= filters.ArchiveDateFrom && search.ArchiveDate <= filters.ArchiveDateTo);

    private Expression<Func<Search, bool>> FilterByFurnitureIds() =>
        search => filters.FurnitureIds == null || filters.FurnitureIds.Length == 0 ||
                  search.SearchesFurniture.Any(f => filters.FurnitureIds.Contains(f.FurnitureId));

    private Expression<Func<Search, bool>> FilterByEstateGroupIds() =>
        search => filters.EstateGroupIds == null || filters.EstateGroupIds.Length == 0 ||
                  search.SearchesEstateGroups.Any(f => filters.EstateGroupIds.Contains(f.EstateGroupId));
}