namespace RealtoCrm.Searches.Models;

using System.Linq;
using AutoMapper;
using Mapping;
using Nomenclatures;
using static CosherConsts.ContractTypes;
using static CosherConsts.SearchStatuses;

public class SearchColorCodingDetailsResponseModel : IMapFrom<Search>, IMapExplicitly
{
    public int? ArchiveReasonId { get; init; }

    public bool IsArchived => this.ArchiveReasonId is not null;

    public ArchiveType? ArchiveType { get; init; }

    public bool IsActiveWithoutContract { get; init; }

    public bool IsActiveWithCommissionContract { get; init; }

    public bool IsActiveWithExclusiveContract { get; init; }

    public bool IsActiveWithCoExclusiveContract { get; init; }

    public bool IsDealSearch { get; init; }

    public virtual void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Search, SearchColorCodingDetailsResponseModel>()
            .ForMember(m => m.ArchiveType, cfg => cfg
                .MapFrom(m => m.ArchiveReason != null
                    ? m.ArchiveReason.ArchiveType
                    : (ArchiveType?)null))
            .ForMember(m => m.IsActiveWithoutContract, cfg => cfg
                .MapFrom(m => m.SearchStatus != null &&
                              m.SearchStatus.Name == ActiveSearchStatusName && m.Contracts.Count == 0))
            .ForMember(m => m.IsActiveWithCommissionContract, cfg => cfg
                .MapFrom(m => m.SearchStatus != null &&
                              m.SearchStatus.Name == ActiveSearchStatusName && m.Contracts
                                  .OrderByDescending(x => x.CreationTime)
                                  .Any(c => c.ContractType != null &&
                                            c.ContractType.Name == CommissionContractTypeName)))
            .ForMember(m => m.IsActiveWithExclusiveContract, cfg => cfg
                .MapFrom(m => m.SearchStatus != null &&
                              m.SearchStatus.Name == ActiveSearchStatusName && m.Contracts
                                  .OrderByDescending(x => x.CreationTime)
                                  .Any(c => c.ContractType != null &&
                                            c.ContractType.Name == ExclusiveContractTypeName)))
            .ForMember(m => m.IsActiveWithCoExclusiveContract, cfg => cfg
                .MapFrom(m => m.SearchStatus != null &&
                              m.SearchStatus.Name == ActiveSearchStatusName && m.Contracts
                                  .OrderByDescending(x => x.CreationTime)
                                  .Any(c => c.ContractType != null &&
                                            c.ContractType.Name == CoexclusiveContractTypeName)))
            .ForMember(m => m.IsDealSearch, cfg => cfg
                .MapFrom(m => m.SearchStatus != null &&
                              m.SearchStatus.Name == DealSearchStatusName));
}