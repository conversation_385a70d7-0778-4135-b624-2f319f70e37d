using AutoMapper;

namespace RealtoCrm.Files.CategoriesSettings.Models;

using Mapping;

public class FileCategorySettingsResponseModel : IMapFrom<FileCategorySetting>, IMapExplicitly
{
    public int  CategoryId { get; set; }

    public string CategoryName { get; init; } = default!;

    public int? ParentId { get; set; }
     public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<FileCategorySetting, FileCategorySettingsResponseModel>()
            .ForMember(m => m.ParentId, cfg => cfg
                .MapFrom(m => m.Category != null ? m.Category.ParentId : null));
}