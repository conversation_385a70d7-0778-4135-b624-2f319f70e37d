namespace RealtoCrm.Files.Categories.Specifications;

using System;
using System.Linq.Expressions;

public class FileCategoryOnlyTemplateSpecification : Specification<FileCategory>
{
    private readonly bool? isTemplate;

    public FileCategoryOnlyTemplateSpecification(bool? isTemplate)
        => this.isTemplate = isTemplate;

    public override Expression<Func<FileCategory, bool>> ToExpression()
    {
        if (this.isTemplate != null)
        {
            return category => category.IsTemplate == this.isTemplate;
        }

        return department => false;
    }
}