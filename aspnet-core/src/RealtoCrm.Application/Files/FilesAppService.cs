namespace RealtoCrm.Files;

using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Authorization;
using Common.Attributes;
using EntityFrameworkCore;
using Expressions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Models;
using Providers;
using Specifications;
using Uploader;

public class FilesAppService(
    IDateTimeService dateTimeService,
    IFilesUploaderAppService filesUploaderAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        File,
        FileCreateRequestModel,
        FileUpdateRequestModel,
        FilePaginatedRequestModel,
        FileDetailsResponseModel,
        FileListingResponseModel>(dbContextProvider, expressionsBuilder), IFilesAppService
{
    private const string BlobContainerName = "documents";

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.DocumentsCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.DocumentsUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.DocumentsDelete)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.DocumentsRead)
            .ConfigureAuthorize(nameof(this.GetAllAsync), AppPermissions.DocumentsRead)
            .ConfigureAuthorize(nameof(this.UploadAndCreateAsync), AppPermissions.DocumentsCreate)
            .AddCustomAttribute(nameof(this.UploadAndCreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UploadAndUpdateAsync), AppPermissions.DocumentsUpdate)
            .AddCustomAttribute(nameof(this.UploadAndUpdateAsync), new RequireTenantAttribute());

    protected override Specification<File> GetSpecification(FilePaginatedRequestModel request)
        => base.GetSpecification(request)
            .And(new FileByIdSpecification(request.Id))
            .And(new FileByCategoryIdSpecification(request.CategoryId))
            .And(new FileByCategoryIsTemplateSpecification(request.CategoryIsTemplate))
            .And(new FileByCategoryNameSpecification(request.CategoryName));

    [Consumes("multipart/form-data")]
    public async Task<int> UploadAndCreateAsync([FromForm] FileCreateRequestModel createRequest)
    {
        var (fileName, fileSource, fileSize) = await this.UploadFileAsync(createRequest.File);

        var fileEntity = new File
        {
            Title = createRequest.Title,
            FileName = fileName,
            Size = fileSize,
            Source = fileSource,
            CategoryId = createRequest.CategoryId,
            ClientId = createRequest.ClientId,
            TenantId = this.GetTenantId(),
        };

        await this.Data.Files.AddAsync(fileEntity);

        await this.Data.SaveChangesAsync();

        return fileEntity.Id;
    }

    [Consumes("multipart/form-data")]
    public async Task<int> UploadAndUpdateAsync(int id, [FromForm] FileCreateRequestModel request)
    {
        var requestModel = this.ObjectMapper.Map<FileUpdateWithNewDocumentRequestModel>(request);

        if (request.File != null)
        {
            var (fileName, fileSource, fileSize) = await this.UploadFileAsync(request.File);

            requestModel.Source = fileSource;
            requestModel.FileName = fileName;
            requestModel.Size = fileSize;
        }

        return await base.UpdateAsync(id, requestModel);
    }

    private async Task<(string FileName, string FileSource, long FileSize)> UploadFileAsync(IFormFile file)
    {
        var fileName = $"{dateTimeService.Now:yyyyMMddTHHmmss}_{file.FileName}";
        var fileSource = await filesUploaderAppService.UploadAsync(file, BlobContainerName, fileName);

        return (fileName, fileSource, file.Length);
    }
}