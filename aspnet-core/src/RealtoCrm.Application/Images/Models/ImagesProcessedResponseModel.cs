namespace RealtoCrm.Images.Models;

public class ImagesProcessedResponseModel
{
    public ImagesProcessedResponseModel(
        byte[] originalJpeg,
        byte[] normalThumbnailJpeg,
        byte[] normalThumbnailWebp,
        byte[] mediumThumbnailWebp,
        byte[] smallThumbnailWebp)
    {
        this.OriginalJpeg = originalJpeg;
        this.NormalThumbnailJpeg = normalThumbnailJpeg;
        this.NormalThumbnailWebp = normalThumbnailWebp;
        this.MediumThumbnailWebp = mediumThumbnailWebp;
        this.SmallThumbnailWebp = smallThumbnailWebp;
    }

    public byte[] OriginalJpeg { get; }

    public byte[] NormalThumbnailJpeg { get; }

    public byte[] NormalThumbnailWebp { get; }

    public byte[] MediumThumbnailWebp { get; }

    public byte[] SmallThumbnailWebp { get; }
}