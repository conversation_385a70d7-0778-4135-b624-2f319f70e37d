namespace RealtoCrm.OffersImages;

using System.Threading.Tasks;
using Abp.Application.Services;
using Images;
using Microsoft.AspNetCore.Http;

public interface IOffersImagesAppService : IApplicationService
{
    Task<ImageThumbnail> UploadThumbnailAsync(
        int tenantId,
        byte[] imageBytes,
        string imageFileName,
        long imageFileSize,
        string imageContentType,
        ThumbnailSize thumbnailSize,
        ThumbnailType thumbnailType);

    Task UploadImagesAsync(int offerId, int tenantId, int imageCategoryId, IFormFileCollection images);
}