namespace RealtoCrm.OffersImages;

using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using BlobStorage;
using EntityFrameworkCore;
using Extensions;
using Files.Uploader;
using Images;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MultiTenancy.Models;
using Offers;
using static CosherConsts.OfferImages;

public class OffersImagesAppService(
    IBlobStorageService blobStorageService,
    IFilesUploaderAppService filesUploaderAppService,
    IImagesProcessorAppService imagesProcessorAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider)
    : ImagesUploaderBaseAppService(
        blobStorageService,
        filesUploaderAppService,
        imagesProcessorAppService,
        dbContextProvider), IOffersImagesAppService
{
    protected override string BlobContainerName => OffersBlobContainerName;

    protected override string GetOriginalImageFileName(int tenantId, int entityId, string fileName)
        => string.Format(OfferBlobFileName, tenantId, entityId, fileName);

    protected override string GetThumbnailFileName(int tenantId, int entityId, int thumbnailSize, string fileName)
        => string.Format(OfferBlobFileNameWithSize, tenantId, entityId, thumbnailSize, fileName);

    [NonAction]
    public async Task UploadImagesAsync(int offerId, int tenantId, int imageCategoryId, IFormFileCollection images)
    {
        var offerTenant = await this.ObjectMapper
            .ProjectTo<TenantIdAndNameResponseModel>(this
                .Data
                .Offers
                .AsNoTracking()
                .Where(o => o.Id == offerId)
                .Select(o => o.Tenant))
            .FirstAsync();

        var hasFeatured = await this
            .Data
            .OffersImages
            .AsNoTracking()
            .Where(oi => oi.OfferId == offerId && !oi.IsDeleted)
            .AnyAsync(oi => oi.IsFeatured);

        var lastImageOrder = await this
            .Data
            .OffersImages
            .AsNoTracking()
            .Where(oi => oi.OfferId == offerId && !oi.IsDeleted)
            .OrderByDescending(oi => oi.Order)
            .Select(oi => oi.Order)
            .FirstOrDefaultAsync();

        var watermarkImageBytes = await this.GetWatermarkImageBytesAsync(offerTenant.Name);

        await images.ForEachAsync(async (image, index) =>
        {
            var imageEntity = await this.ProcessAndUploadImageWithThumbnailsAsync(
                image,
                watermarkImageBytes,
                tenantId,
                offerId,
                imageCategoryId);

            await this.Data.OffersImages.AddAsync(new OfferImage
            {
                Order = ++lastImageOrder,
                OfferId = offerId,
                IsActive = true,
                IsFeatured = !hasFeatured && index == 0,
                Image = imageEntity,
            });
        });

        await this.Data.SaveChangesAsync();
    }
}