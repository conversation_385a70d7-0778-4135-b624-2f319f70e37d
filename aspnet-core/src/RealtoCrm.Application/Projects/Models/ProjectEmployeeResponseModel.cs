namespace RealtoCrm.Projects.Models;

using Addresses.Models;
using AutoMapper;
using Mapping;

public class ProjectEmployeeResponseModel : IMapFrom<ProjectEmployee>, IMapExplicitly
{
    public int EmployeeId { get; init; }

    public long EmployeeUserId { get; init; }

    public string EmployeeName { get; init; } = default!;

    public string EmployeePhoneNumber { get; init; } = default!;

    public string EmployeeEmailAddress { get; init; } = default!;

    public AddressResponseModel? EmployeeOfficeAddress { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<ProjectEmployee, ProjectEmployeeResponseModel>()
            .ForMember(m => m.EmployeeUserId, cfg => cfg
                .MapFrom(m => m.Employee.UserAccount.UserId))
            .ForMember(m => m.EmployeeName, cfg => cfg
                .MapFrom(m => $"{m.Employee.FirstName} {m.Employee.LastName}"))
            .ForMember(m => m.EmployeeEmailAddress, cfg => cfg
                .MapFrom(m => m.Employee.UserAccount.EmailAddress));
}