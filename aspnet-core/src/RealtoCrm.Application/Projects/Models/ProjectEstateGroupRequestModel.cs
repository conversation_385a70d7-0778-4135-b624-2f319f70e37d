namespace RealtoCrm.Projects.Models;

using System.Collections.Generic;
using Addresses.Models;
using Buildings.Models;
using EstateGroups.Models;
using EstateObjects;
using Mapping;

public class ProjectEstateGroupRequestModel : IMapTo<EstateGroup>
{
    public int Id { get; init; }

    public string Name { get; init; } = default!;

    public string? CadastralNumber { get; init; }

    public string? ZonedProperty { get; init; }

    public string? ZonedPropertyNumber { get; init; }

    public string? Description { get; init; }

    public int TenantId { get; set; }

    public AddressRequestModel Address { get; init; } = default!;

    public EstateGroupDetailRequestModel EstateGroupDetail { get; init; } = default!;

    public IEnumerable<BuildingRequestModel> Buildings { get; init; } = [];

    public IEnumerable<ProjectEstateGroupHeatingRequestModel> EstateGroupsHeatings { get; init; } = [];
}