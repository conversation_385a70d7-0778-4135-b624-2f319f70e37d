namespace RealtoCrm.Projects.Specifications;

using System;
using System.Linq.Expressions;

public class ProjectByIdSpecification : Specification<Project>
{
    private readonly int? id;

    public ProjectByIdSpecification(int? id) => this.id = id;

    protected override bool Include => this.id is not null;

    public override Expression<Func<Project, bool>> ToExpression()
        => project => project.Id == this.id;
}