using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.CMSExport.NomenclatureExport.PopulatedPlacesExport.Models;

public class PopulatedPlaceExportModel : IMapFrom<PopulatedPlace>, IMapExplicitly
{
    public int Id { get; set; }

    public string Name { get; set; } = default!;

    public int MunicipalityId { get; set; }

    public int ProvinceId { get; set; }

    public int CountryId { get; set; }

    public string Code { get; set; }

    public List<TranslationExportModel>? Translations { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<PopulatedPlace, PopulatedPlaceExportModel>()
            .ForMember(slm => slm.ProvinceId, cfg => cfg
                .MapFrom(s => s.Municipality.ProvinceId))
            .ForMember(slm => slm.CountryId, cfg => cfg
                .MapFrom(s => s.Municipality.Province.CountryId)).ForMember(dest => dest.Translations,
                opt =>
                {
                    opt.Condition(src => src.Translations != null && src.Translations.Count > 0);
                    opt.MapFrom(src => src.Translations.Select(t => new TranslationExportModel
                    {
                        LanguageId = t.LanguageId,
                        Name = t.Name
                    }));
                });
}