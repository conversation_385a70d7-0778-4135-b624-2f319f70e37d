using System;
using System.Threading.Tasks;
using Abp.Authorization;
using Abp.Webhooks;
using RealtoCrm.Authorization;

namespace RealtoCrm.WebHooks;

[AbpAuthorize]
public class WebhookEventAppService(IWebhookEventStore webhookEventStore) : RealtoCrmAppServiceBase, IWebhookEventAppService
{
    public async Task<WebhookEvent> Get(string id)
    {
        return await webhookEventStore.GetAsync(this.AbpSession.TenantId, Guid.Parse(id));
    }
}