namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;

public class ClientBySectorsSpecification(int[]? sectors) : Specification<Client>
{
    protected override bool Include => sectors != null;
    
    public override Expression<Func<Client, bool>> ToExpression()
        => client => client.ClientsSectors != null && 
                     client.ClientsSectors.Any(c => sectors.Contains(c.SectorId));
}