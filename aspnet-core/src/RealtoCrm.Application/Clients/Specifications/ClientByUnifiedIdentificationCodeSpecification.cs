namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq.Expressions;

public class ClientByUnifiedIdentificationCodeSpecification(string? unifiedIdentificationCode) : Specification<Client>
{
    protected override bool Include => !string.IsNullOrWhiteSpace(unifiedIdentificationCode);

    public override Expression<Func<Client, bool>> ToExpression()
        => client =>
            client.LegalEntity != null &&
            client.LegalEntity.UnifiedIdentificationCode == unifiedIdentificationCode!;
}