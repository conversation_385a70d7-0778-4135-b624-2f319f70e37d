using AutoMapper;
using RealtoCrm.Addresses.Models;

namespace RealtoCrm.Clients.Models;

using RealtoCrm.Mapping;
using RealtoCrm.Addresses;

public class ClientAddressesRequestModel : IMapTo<ClientAddress>, IMapTo<Address>, IMapTo<BaseAddressRequestModel>,
    IMapExplicitly
{
    public int? AddressId { get; set; }

    public int? CountryId { get; set; }

    public int? ProvinceId { get; set; }

    public int? MunicipalityId { get; set; }

    public int? PopulatedPlaceId { get; set; }

    public int? DistrictId { get; set; }

    public int? StreetId { get; set; }

    public string? StreetNumber { get; set; }

    public string? BlockNumber { get; set; }

    public string? EntranceNumber { get; set; }

    public string? FloorNumber { get; set; }

    public string? ApartmentNumber { get; set; }

    public int? AddressTypeId { get; set; }
    
    public int? TenantId { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<ClientAddressesRequestModel, ClientAddress>()
            .ForMember(dest => dest.AddressId, opt
                => opt.MapFrom(src => src.AddressId!));
}