namespace RealtoCrm.Clients.Models;

using System.Linq;
using AutoMapper;
using RealtoCrm.ContactDetails.Models;
using RealtoCrm.Mapping;
using static CosherConsts.ClientTypes;
using static CosherConsts.ContactDetails;

public class ClientsRelatedClientsResponseModel : IMapFrom<RelatedClient>, IMapExplicitly
{
    public int Id { get; init; }

    public int ClientId { get; set; }

    public int RelatedClientId { get; set; }

    public string RelatedClientFirstName { get; set; } = default!;

    public string RelatedClientMiddleName { get; set; } = default!;

    public string RelatedClientLastName { get; set; } = default!;

    public int RelatedClientTypeId { get; set; } = default!;

    public string RelatedClientTypeName { get; set; } = default!;

    public int ClientTypeId { get; set; } = default!;

    public string Email { get; set; } = default!;

    public string PhoneNumber { get; set; } = default!;

    public string Name { get; set; } = default!;

    public string UnifiedIdentificationCode { get; set; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<RelatedClient, ClientsRelatedClientsResponseModel>()
            .ForMember(dest => dest.RelatedClientFirstName, opt
                => opt.MapFrom(src => src.Client.Type.Name == LegalClientTypeName
                    ? src.ClientRelatedClient.PersonalData!.FirstName
                    : src.ClientRelatedClient.Type.Name == PersonalClientTypeName
                        ? src.ClientRelatedClient.PersonalData!.FirstName
                        : null))
            .ForMember(dest => dest.RelatedClientId, opt
                => opt.MapFrom(src => src.Client.Type.Name == LegalClientTypeName
                    ? src.ClientRelatedClientId
                    : src.Client.Id))
            .ForMember(dest => dest.RelatedClientMiddleName, opt
                => opt.MapFrom(src => src.Client.Type.Name == LegalClientTypeName
                    ? src.ClientRelatedClient.PersonalData!.MiddleName
                    : src.ClientRelatedClient.Type.Name == PersonalClientTypeName
                        ? src.ClientRelatedClient.PersonalData!.MiddleName
                        : null))
            .ForMember(dest => dest.RelatedClientLastName, opt
                => opt.MapFrom(src => src.Client.Type.Name == LegalClientTypeName
                    ? src.ClientRelatedClient.PersonalData!.LastName
                    : src.ClientRelatedClient.Type.Name == PersonalClientTypeName
                        ? src.ClientRelatedClient.PersonalData!.LastName
                        : null))
            .ForMember(dest => dest.PhoneNumber, opt
                => opt.MapFrom(src =>
                    src.ClientRelatedClient.ContactDetails!.First(x => x.ContactDetail.Name == PhoneContactDetailName)
                        .Value))
            .ForMember(dest => dest.Email, opt
                => opt.MapFrom(src =>
                    src.ClientRelatedClient.ContactDetails!.First(x => x.ContactDetail.Name == EmailContactDetailName)
                        .Value))
            .ForMember(dest => dest.ClientTypeId, opt
                => opt.MapFrom(src =>
                    src.Client.Type.Name == LegalClientTypeName ? src.Client.TypeId : src.ClientRelatedClient.TypeId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src
                => src.Client.Type.Name == LegalClientTypeName
                    ? src.Client.LegalEntity!.Name
                    : src.ClientRelatedClient.LegalEntity!.Name
            ))
            .ForMember(dest => dest.UnifiedIdentificationCode, opt => opt.MapFrom(src
                => src.Client.Type.Name == LegalClientTypeName
                    ? src.Client.LegalEntity!.UnifiedIdentificationCode
                    : src.ClientRelatedClient.LegalEntity!.UnifiedIdentificationCode
            ))
            .ForMember(dest => dest.ClientId, opt => opt.MapFrom(src
                => src.Client.Type.Name == LegalClientTypeName
                    ? src.ClientId
                    : src.ClientRelatedClientId
            ));
}