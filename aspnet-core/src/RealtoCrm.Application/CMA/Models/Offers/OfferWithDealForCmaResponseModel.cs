namespace RealtoCrm.CMA.Models;

using System.Linq;
using AutoMapper;
using RealtoCrm.CMA.Models.Common;
using RealtoCrm.Deals;
using RealtoCrm.Offers;

public class OfferWithDealForCmaResponseModel : OfferForCmaBaseResponseModel
{
    public DealForCmaResponseModel Deal { get; init; }

    public override void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<Offer, OfferWithDealForCmaResponseModel>()
            .IncludeBase<Offer, OfferForCmaBaseResponseModel>()
            .ForMember(
                dest => dest.Deal,
                opt
                    => opt.MapFrom(o => o.Deals
                        .FirstOrDefault(d => d.DealStatus == DealStatus.IsComplete)));
}