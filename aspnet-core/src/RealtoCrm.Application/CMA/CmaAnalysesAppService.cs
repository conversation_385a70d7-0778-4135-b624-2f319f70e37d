namespace RealtoCrm.CMA;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NUglify.Helpers;
using RealtoCrm.Application.CMA;
using RealtoCrm.Cma;
using RealtoCrm.CMA.Models.CmaAnalyses;
using RealtoCrm.CMA.Models.Common;
using RealtoCrm.CMA.ValueResolvers;
using RealtoCrm.DataCrudModels;
using RealtoCrm.Employees;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Expressions;
using RealtoCrm.Files.Uploader;
using RealtoCrm.Offers;
using RealtoCrm.Searches;

public class CmaAnalysesAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    CmaPdfAppService cmaPdfAppService,
    IExpressionsBuilder expressionsBuilder,
    IFilesUploaderAppService filesUploaderAppService,
    IEmployeesAppService employeesAppService
) : DataCrudAppService<
    int,
    CmaAnalysis,
    CmaAnalysisCreateRequestModel,
    NoUpdateModel<CmaAnalysis>,
    PaginatedRequestModel,
    CmaAnalysisDetailsResponseModel,
    CmaAnalysisListingResponseModel
>(dbContextProvider, expressionsBuilder)
{
    public override async Task<int> CreateAsync(CmaAnalysisCreateRequestModel request)
    {
        if (!request.Id.HasValue)
        {
            var cmaAnalysis = new CmaAnalysis
            {
                BaseOfferId = request.BaseOfferId,
            };
            this.Data.Add(cmaAnalysis);
            await this.Data.SaveChangesAsync();
            request.Id = cmaAnalysis.Id;
        }

        var entity = await this.Update(request);
        await this.PublishIfReady(request, entity);

        return entity.Id;
    }

    private async Task PublishIfReady(CmaAnalysisCreateRequestModel request, CmaAnalysis entity)
    {
        if (request.State is not (CmaAnalysisState.ReadyForPublishing or CmaAnalysisState.Published))
        {
            return;
        }

        var result = await cmaPdfAppService.RenderCmaOffersPdf(entity.Id);

        // Convert base64 to byte array
        var bytes = Convert.FromBase64String(result);

        // Create a memory stream from the bytes
        var stream = new MemoryStream(bytes);

        var employee = await employeesAppService.GetDetailsAsync((int)this.AbpSession.UserId);
        var employeeName = employee.FirstName + " " + employee.LastName;

        var fileName = $"cma-analysis-on-{DateTime.Now}-by-{employeeName}.pdf";
        var file = new FormFile(
            baseStream: stream,
            baseStreamOffset: 0,
            length: stream.Length,
            name: fileName,
            fileName: $"{entity.Id}.pdf"
        )
        {
            Headers = new HeaderDictionary(),
            ContentType = "application/pdf"
        };

        var url = await filesUploaderAppService.UploadAsync(file, "cma", fileName);
        entity.PdfUrl = url;
        await this.Data.SaveChangesAsync();
    }

    private async Task<CmaAnalysis> Update(CmaAnalysisCreateRequestModel request)
    {
        var entity = await this.Data.Set<CmaAnalysis>()
            .Include(x => x.SelectedOffers)
            .Include(x => x.SelectedOffersWithDeals)
            .Include(x => x.SelectedUnfulfilledOffers)
            .Include(x => x.SelectedSearches)
            .FirstAsync(x => x.Id == request.Id.Value);

        entity.SelectedOffers.Clear();
        entity.SelectedOffersWithDeals.Clear();
        entity.SelectedUnfulfilledOffers.Clear();
        entity.SelectedSearches.Clear();

        await this.Data.SaveChangesAsync();

        // Map new relations
        var dependencyMappings = await this.GetCreateDependencyMappings(request);
        this.ObjectMapper.Map(request, entity, ApplyDependencies(dependencyMappings));

        await this.Data.SaveChangesAsync();
        return entity;
    }

    private static Action<IMappingOperationOptions<CmaAnalysisCreateRequestModel, CmaAnalysis>> ApplyDependencies(IDictionary<string, object> dependencyMappings)
        => opt => dependencyMappings.ForEach(d => opt.Items[d.Key] = d.Value);

    private async Task<IDictionary<string, object>> GetCreateDependencyMappings(CmaAnalysisCreateRequestModel request)
    {
        var allOfferIds = new HashSet<int>([
            .. request.SelectedOffersIds,
            .. request.SelectedOffersWithDealsIds,
            .. request.SelectedUnfulfilledOffersIds
        ]);

        var offers = await this.Data.Set<Offer>()
            .IgnoreQueryFilters()
            .Where(o => allOfferIds.Contains(o.Id))
            .ToListAsync();

        var searches = await this.Data.Set<Search>()
            .IgnoreQueryFilters()
            .Where(s => request.SelectedSearchesIds.Contains(s.Id))
            .ToListAsync();

        return new Dictionary<string, object>
        {
            [OffersByIdsValueResolver.OffersItemsKey] = offers,
            [SearchesByIdsValueResolver.SearchesItemsKey] = searches,
        };
    }
}