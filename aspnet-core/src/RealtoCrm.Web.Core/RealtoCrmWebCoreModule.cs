using System.IO;
using System.Text;
using Abp.AspNetCore;
using Abp.AspNetCore.Configuration;
using Abp.AspNetCore.SignalR;
using Abp.AspNetZeroCore.Web;
using Abp.Configuration.Startup;
using Abp.Hangfire;
using Abp.Hangfire.Configuration;
using Abp.Modules;
using Abp.Reflection.Extensions;
using Abp.Runtime.Caching.Redis;
using Abp.Zero.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using RealtoCrm.Authentication.TwoFactor;
using RealtoCrm.Configuration;
using RealtoCrm.EntityFrameworkCore;
using RealtoCrm.Web.Authentication.JwtBearer;
using RealtoCrm.Web.Common;
using RealtoCrm.Web.Configuration;
using Abp.HtmlSanitizer;
using Abp.HtmlSanitizer.Configuration;
using RealtoCrm.Authorization.Accounts;
using Microsoft.Extensions.Hosting;

namespace RealtoCrm.Web;

[DependsOn(
    typeof(RealtoCrmApplicationModule),
    typeof(RealtoCrmEntityFrameworkCoreModule),
    typeof(AbpAspNetZeroCoreWebModule),
    typeof(AbpAspNetCoreSignalRModule),
    typeof(AbpRedisCacheModule), //AbpRedisCacheModule dependency (and Abp.RedisCache nuget package) can be removed if not using Redis cache
    typeof(AbpHangfireAspNetCoreModule), //AbpHangfireModule dependency (and Abp.Hangfire.AspNetCore nuget package) can be removed if not using Hangfire
    typeof(AbpHtmlSanitizerModule)
)]
public class RealtoCrmWebCoreModule(IWebHostEnvironment env) : AbpModule
{
    private readonly IConfigurationRoot appConfiguration = env.GetAppConfiguration();

    public override void PreInitialize()
    {
        //Set default connection string
        this.Configuration.DefaultNameOrConnectionString = this.appConfiguration.GetConnectionString(
            RealtoCrmConsts.ConnectionStringName
        );

        //Use database for language management
        this.Configuration.Modules.Zero().LanguageManagement.EnableDbLocalization();

        this.Configuration.Modules.AbpAspNetCore()
            .CreateControllersForAppServices(
                typeof(RealtoCrmApplicationModule).GetAssembly()
            );

        this.Configuration.Caching.Configure(TwoFactorCodeCacheItem.CacheName,
            cache => { cache.DefaultSlidingExpireTime = TwoFactorCodeCacheItem.DefaultSlidingExpireTime; });

        if (this.appConfiguration["Authentication:JwtBearer:IsEnabled"] != null &&
            bool.Parse(this.appConfiguration["Authentication:JwtBearer:IsEnabled"]))
        {
            this.ConfigureTokenAuth();
        }

        this.Configuration.ReplaceService<IAppConfigurationAccessor, AppConfigurationAccessor>();

        this.Configuration.ReplaceService<IAppConfigurationWriter, AppConfigurationWriter>();

        if (WebConsts.HangfireDashboardEnabled)
        {
            this.Configuration.BackgroundJobs.UseHangfire();
        }

        //Use Redis cache in production instead of in-memory cache.
        //See app.config for Redis configuration and connection string
        if (env.IsProduction())
        {
            Configuration.Caching.UseRedis(options =>
            {
                options.ConnectionString = appConfiguration["Abp:RedisCache:ConnectionString"];
                options.DatabaseId = appConfiguration.GetValue<int>("Abp:RedisCache:DatabaseId");
            });
        }

        // HTML Sanitizer configuration
        this.Configuration.Modules.AbpHtmlSanitizer()
            .KeepChildNodes()
            .AddSelector<IAccountAppService>(x => nameof(x.IsTenantAvailable))
            .AddSelector<IAccountAppService>(x => nameof(x.Register));
    }

    private void ConfigureTokenAuth()
    {
        this.IocManager.Register<TokenAuthConfiguration>();
        var tokenAuthConfig = this.IocManager.Resolve<TokenAuthConfiguration>();

        tokenAuthConfig.SecurityKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(this.appConfiguration["Authentication:JwtBearer:SecurityKey"])
        );

        tokenAuthConfig.Issuer = this.appConfiguration["Authentication:JwtBearer:Issuer"];
        tokenAuthConfig.Audience = this.appConfiguration["Authentication:JwtBearer:Audience"];
        tokenAuthConfig.SigningCredentials =
            new SigningCredentials(tokenAuthConfig.SecurityKey, SecurityAlgorithms.HmacSha256);
        tokenAuthConfig.AccessTokenExpiration = AppConsts.AccessTokenExpiration;
        tokenAuthConfig.RefreshTokenExpiration = AppConsts.RefreshTokenExpiration;
    }

    public override void Initialize()
    {
        this.IocManager.RegisterAssemblyByConvention(typeof(RealtoCrmWebCoreModule).GetAssembly());
    }

    public override void PostInitialize()
    {
        this.SetAppFolders();

        this.IocManager.Resolve<ApplicationPartManager>()
            .AddApplicationPartsIfNotAddedBefore(typeof(RealtoCrmWebCoreModule).Assembly);
    }

    private void SetAppFolders()
    {
        var appFolders = this.IocManager.Resolve<AppFolders>();

        appFolders.SampleProfileImagesFolder = Path.Combine(env.WebRootPath,
            $"Common{Path.DirectorySeparatorChar}Images{Path.DirectorySeparatorChar}SampleProfilePics");
        appFolders.WebLogsFolder = Path.Combine(env.ContentRootPath, $"App_Data{Path.DirectorySeparatorChar}Logs");
    }
}