using System;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using Abp.Auditing;
using Abp.Extensions;
using Abp.MimeTypes;
using Microsoft.AspNetCore.Mvc;
using RealtoCrm.Dto;
using RealtoCrm.Storage;

namespace RealtoCrm.Web.Controllers;

public class FileController(
    ITempFileCacheManager tempFileCacheManager,
    IBinaryObjectManager binaryObjectManager,
    IMimeTypeMap mimeTypeMap) : RealtoCrmControllerBase
{
    [DisableAuditing]
    public ActionResult DownloadTempFile(FileDto file)
    {
        var fileBytes = tempFileCacheManager.GetFile(file.FileToken);
        if (fileBytes == null)
        {
            return this.NotFound(this.L("RequestedFileDoesNotExists"));
        }

        return this.File(fileBytes, file.FileType, file.FileName);
    }

    [DisableAuditing]
    public async Task<ActionResult> DownloadBinaryFile(Guid id, string contentType, string fileName)
    {
        var fileObject = await binaryObjectManager.GetOrNullAsync(id);
        if (fileObject == null)
        {
            return this.StatusCode((int) HttpStatusCode.NotFound);
        }

        if (fileName.IsNullOrEmpty())
        {
            if (!fileObject.Description.IsNullOrEmpty() &&
                !Path.GetExtension(fileObject.Description).IsNullOrEmpty())
            {
                fileName = fileObject.Description;
            }
            else
            {
                return this.StatusCode((int) HttpStatusCode.BadRequest);
            }
        }

        if (contentType.IsNullOrEmpty())
        {
            if (!Path.GetExtension(fileName).IsNullOrEmpty())
            {
                contentType = mimeTypeMap.GetMimeType(fileName);
            }
            else
            {
                return this.StatusCode((int) HttpStatusCode.BadRequest);
            }
        }

        return this.File(fileObject.Bytes, contentType, fileName);
    }
}