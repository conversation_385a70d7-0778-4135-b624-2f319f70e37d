using System;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Web.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RealtoCrm.Notifications;

namespace RealtoCrm.Web.Controllers;

public class BrowserCacheCleanerController(INotificationAppService notificationAppService) : RealtoCrmControllerBase
{
    public async Task<IActionResult> Clear()
    {
        var result = await notificationAppService.SetAllAvailableVersionNotificationAsRead();

        this.HttpContext.Response.Headers.Append("Clear-Site-Data", "\"cache\"");

        return this.Json(new {Result = result});
    }
}