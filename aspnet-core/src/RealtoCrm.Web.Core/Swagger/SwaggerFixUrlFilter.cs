using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;

namespace RealtoCrm.Web.Swagger;

using DotSwashbuckle.AspNetCore.SwaggerGen;

public class SwaggerFixUrlFilter : IOperationFilter
{
    private string ServerRootAddress => Environment.GetEnvironmentVariable("App__ServerRootAddress");

    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Servers = new List<OpenApiServer>
        {
            new() { Url = this.ServerRootAddress }
        };
    }
}