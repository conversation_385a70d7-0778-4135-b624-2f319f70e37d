using System.Threading.Tasks;
using Abp;
using Abp.Configuration;
using RealtoCrm.Configuration;
using RealtoCrm.Configuration.Dto;
using RealtoCrm.UiCustomization;
using RealtoCrm.UiCustomization.Dto;

namespace RealtoCrm.Web.UiCustomization.Metronic;

public class Theme7UiCustomizer(ISettingManager settingManager) : UiThemeCustomizerBase(settingManager, AppConsts.Theme7), IUiCustomizer
{
    public async Task<UiCustomizationSettingsDto> GetUiSettings()
    {
        var settings = new UiCustomizationSettingsDto
        {
            BaseSettings = new ThemeSettingsDto
            {
                Layout = new ThemeLayoutSettingsDto()
                {
                    LayoutType = "fluid-xxl",
                    DarkMode = await this.GetSettingValueAsync<bool>(AppSettings.UiManagement.DarkMode)
                },
                Footer = new ThemeFooterSettingsDto
                {
                    FixedFooter = await this.GetSettingValueAsync<bool>(AppSettings.UiManagement.Footer.FixedFooter)
                },
                Menu = new ThemeMenuSettingsDto()
                {
                    SearchActive = await this.GetSettingValueAsync<bool>(AppSettings.UiManagement.SearchActive),
                    EnableSecondary = true,
                    DefaultMinimizedAside = false,
                    FixedAside = true
                }
            }
        };

        settings.BaseSettings.Theme = this.ThemeName;
        settings.BaseSettings.Layout.LayoutType = "boxed";
        settings.BaseSettings.Menu.Position = "left";
        settings.BaseSettings.Menu.AsideSkin = "light";
        settings.BaseSettings.Menu.SubmenuToggle = "false";

        settings.BaseSettings.SubHeader.SubheaderSize = 1;
        settings.BaseSettings.SubHeader.TitleStyle = "text-dark fw-bold my-0 fs-2";
        settings.BaseSettings.SubHeader.ContainerStyle = "subheader py-2 py-lg-4  subheader-transparent";

        settings.IsLeftMenuUsed = true;
        settings.IsTopMenuUsed = false;
        settings.IsTabMenuUsed = false;
        settings.AllowMenuScroll = false;

        return settings;
    }

    public async Task UpdateUserUiManagementSettingsAsync(UserIdentifier user, ThemeSettingsDto settings)
    {
        await this.SettingManager.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.DarkMode,
            settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Header.DesktopFixedHeader,
            settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Header.MobileFixedHeader,
            settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.SubHeader.Fixed,
            settings.SubHeader.FixedSubHeader.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.SubHeader.Style,
            settings.SubHeader.SubheaderStyle);
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Footer.FixedFooter,
            settings.Footer.FixedFooter.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.SearchActive,
            settings.Menu.SearchActive.ToString());
    }

    public async Task UpdateTenantUiManagementSettingsAsync(int tenantId, ThemeSettingsDto settings, UserIdentifier changerUser)
    {
        await this.SettingManager.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.DarkMode,
            settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Header.DesktopFixedHeader,
            settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Header.MobileFixedHeader,
            settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.SubHeader.Fixed,
            settings.SubHeader.FixedSubHeader.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.SubHeader.Style,
            settings.SubHeader.SubheaderStyle);
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Footer.FixedFooter,
            settings.Footer.FixedFooter.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.SearchActive,
            settings.Menu.SearchActive.ToString());
            
        await this.ResetDarkModeSettingsAsync(changerUser);
    }

    public async Task UpdateApplicationUiManagementSettingsAsync(ThemeSettingsDto settings, UserIdentifier changerUser)
    {
        await this.SettingManager.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.DarkMode,
            settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Header.DesktopFixedHeader,
            settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Header.MobileFixedHeader,
            settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.SubHeader.Fixed,
            settings.SubHeader.FixedSubHeader.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.SubHeader.Style,
            settings.SubHeader.SubheaderStyle);
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Footer.FixedFooter,
            settings.Footer.FixedFooter.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.SearchActive,
            settings.Menu.SearchActive.ToString());
            
        await this.ResetDarkModeSettingsAsync(changerUser);
    }

    public async Task<ThemeSettingsDto> GetHostUiManagementSettings()
    {
        var theme = await this.SettingManager.GetSettingValueForApplicationAsync(AppSettings.UiManagement.Theme);

        return new ThemeSettingsDto
        {
            Theme = theme,
            Layout = new ThemeLayoutSettingsDto()
            {
                LayoutType = "fluid-xxl",
                DarkMode = await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.DarkMode),
            },
            Header = new ThemeHeaderSettingsDto(),
            SubHeader = new ThemeSubHeaderSettingsDto
            {
                FixedSubHeader =
                    await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.SubHeader.Fixed),
                SubheaderStyle = await this.GetSettingValueForApplicationAsync(AppSettings.UiManagement.SubHeader.Style)
            },
            Footer = new ThemeFooterSettingsDto
            {
                FixedFooter =
                    await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.Footer.FixedFooter)
            },
            Menu = new ThemeMenuSettingsDto()
            {
                SearchActive = await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.SearchActive)
            }
        };
    }

    public async Task<ThemeSettingsDto> GetTenantUiCustomizationSettings(int tenantId)
    {
        var theme = await this.SettingManager.GetSettingValueForTenantAsync(AppSettings.UiManagement.Theme, tenantId);

        return new ThemeSettingsDto
        {
            Theme = theme,
            Layout = new ThemeLayoutSettingsDto()
            {
                LayoutType = "fluid-xxl",
                DarkMode = await this.GetSettingValueForTenantAsync<bool>(AppSettings.UiManagement.DarkMode, tenantId)
            },
            SubHeader = new ThemeSubHeaderSettingsDto
            {
                FixedSubHeader = await this.GetSettingValueForTenantAsync<bool>(
                    AppSettings.UiManagement.SubHeader.Fixed,
                    tenantId
                ),
                SubheaderStyle = await this.GetSettingValueForTenantAsync(
                    AppSettings.UiManagement.SubHeader.Style,
                    tenantId
                )
            },
            Footer = new ThemeFooterSettingsDto
            {
                FixedFooter = await this.GetSettingValueForTenantAsync<bool>(
                    AppSettings.UiManagement.Footer.FixedFooter,
                    tenantId
                )
            },
            Menu = new ThemeMenuSettingsDto()
            {
                SearchActive =
                    await this.GetSettingValueForTenantAsync<bool>(
                        AppSettings.UiManagement.SearchActive,
                        tenantId
                    )
            }
        };
    }

    public override Task<string> GetBodyClass()
    {
        return Task.FromResult("header-fixed header-tablet-and-mobile-fixed aside-fixed aside-secondary-enabled");
    }
}