using System;
using System.Threading.Tasks;
using Abp;
using Abp.Configuration;
using Abp.Extensions;
using RealtoCrm.Configuration;

namespace RealtoCrm.Web.UiCustomization.Metronic;

public class UiThemeCustomizerBase(ISettingManager settingManager, string themeName)
{
    protected ISettingManager SettingManager = settingManager;
    protected string ThemeName = themeName;

    protected async Task<string> GetSettingValueAsync(string settingName)
    {
        return await this.SettingManager.GetSettingValueAsync(this.ThemeName + "." + settingName);
    }

    protected async Task<T> GetSettingValueAsync<T>(string settingName) where T : struct
    {
        return (await this.SettingManager.GetSettingValueAsync(this.ThemeName + "." + settingName)).To<T>();
    }

    protected async Task<string> GetSettingValueForApplicationAsync(string settingName)
    {
        return await this.SettingManager.GetSettingValueForApplicationAsync(this.ThemeName + "." + settingName);
    }

    protected async Task<T> GetSettingValueForApplicationAsync<T>(string settingName) where T : struct
    {
        return (await this.SettingManager.GetSettingValueForApplicationAsync(this.ThemeName + "." + settingName)).To<T>();
    }

    protected async Task<string> GetSettingValueForTenantAsync(string settingName, int tenantId)
    {
        return await this.SettingManager.GetSettingValueForTenantAsync(this.ThemeName + "." + settingName, tenantId);
    }

    protected async Task<T> GetSettingValueForTenantAsync<T>(string settingName, int tenantId) where T : struct
    {
        return (await this.SettingManager.GetSettingValueForTenantAsync(this.ThemeName + "." + settingName, tenantId))
            .To<T>();
    }

    protected async Task ChangeSettingForUserAsync(UserIdentifier user, string name, string value)
    {
        await this.SettingManager.ChangeSettingForUserAsync(user, this.ThemeName + "." + name, value);
    }

    protected async Task ChangeSettingForTenantAsync(int tenantId, string name, string value)
    {
        await this.SettingManager.ChangeSettingForTenantAsync(tenantId, this.ThemeName + "." + name, value);
    }

    protected async Task ChangeSettingForApplicationAsync(string name, string value)
    {
        await this.SettingManager.ChangeSettingForApplicationAsync(this.ThemeName + "." + name, value);
    }

    public virtual async Task UpdateDarkModeSettingsAsync(UserIdentifier user, bool isDarkModeEnabled)
    {
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.DarkMode, isDarkModeEnabled.ToString());
    }

    protected virtual async Task ResetDarkModeSettingsAsync(UserIdentifier user)
    {
        string applicationDefault;
        if (user.TenantId.HasValue)
        {
            applicationDefault = await this.GetSettingValueForTenantAsync(AppSettings.UiManagement.DarkMode, user.TenantId.Value);
        }
        else
        {

            applicationDefault = await this.GetSettingValueForApplicationAsync(AppSettings.UiManagement.DarkMode);
        }

        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.DarkMode, applicationDefault);
    }
        
    public virtual Task<string> GetBodyClass()
    {
        return Task.FromResult("app-default");
    }
        
    public virtual Task<string> GetBodyStyle()
    {
        return Task.FromResult("");
    }
}