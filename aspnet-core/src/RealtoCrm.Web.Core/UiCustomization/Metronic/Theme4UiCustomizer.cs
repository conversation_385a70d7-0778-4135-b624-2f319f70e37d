using System.Threading.Tasks;
using Abp;
using Abp.Configuration;
using RealtoCrm.Configuration;
using RealtoCrm.Configuration.Dto;
using RealtoCrm.UiCustomization;
using RealtoCrm.UiCustomization.Dto;

namespace RealtoCrm.Web.UiCustomization.Metronic;

public class Theme4UiCustomizer(ISettingManager settingManager) : UiThemeCustomizerBase(settingManager, AppConsts.Theme4), IUiCustomizer
{
    public async Task<UiCustomizationSettingsDto> GetUiSettings()
    {
        var settings = new UiCustomizationSettingsDto
        {
            BaseSettings = new ThemeSettingsDto
            {
                Layout = new ThemeLayoutSettingsDto
                {
                    LayoutType = await this.GetSettingValueAsync(AppSettings.UiManagement.LayoutType),
                    DarkMode = await this.GetSettingValueAsync<bool>(AppSettings.UiManagement.DarkMode)
                },
                Header = new ThemeHeaderSettingsDto
                {
                    MinimizeDesktopHeaderType = await this.GetSettingValueAsync(AppSettings.UiManagement.Header.MinimizeType)
                },
                Menu = new ThemeMenuSettingsDto()
                {
                    SearchActive = await this.GetSettingValueAsync<bool>(AppSettings.UiManagement.SearchActive)
                }
            }
        };

        settings.BaseSettings.Theme = this.ThemeName;
        settings.BaseSettings.Menu.FixedAside = true;
        settings.BaseSettings.Menu.Position = "top";
        settings.BaseSettings.Menu.AsideSkin = "light";
        settings.BaseSettings.Menu.EnableSecondary = false;

        settings.BaseSettings.SubHeader.TitleStyle = "text-white me-5 mb-0";
        settings.BaseSettings.SubHeader.ContainerStyle = "subheader pt-10 pb-8 toolbar";
            
        settings.IsLeftMenuUsed = false;
        settings.IsTopMenuUsed = true;
        settings.IsTabMenuUsed = false;

        return settings;
    }

    public async Task UpdateUserUiManagementSettingsAsync(UserIdentifier user, ThemeSettingsDto settings)
    {
        await this.SettingManager.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.DarkMode, settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.LayoutType, settings.Layout.LayoutType);
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Header.DesktopFixedHeader, settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Header.MobileFixedHeader, settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Header.MinimizeType, settings.Header.MinimizeDesktopHeaderType);
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.SearchActive, settings.Menu.SearchActive.ToString());
    }

    public async Task UpdateTenantUiManagementSettingsAsync(int tenantId, ThemeSettingsDto settings, UserIdentifier changerUser)
    {
        await this.SettingManager.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.DarkMode, settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.LayoutType, settings.Layout.LayoutType);
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Header.DesktopFixedHeader, settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Header.MobileFixedHeader, settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Header.MinimizeType, settings.Header.MinimizeDesktopHeaderType);
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.SearchActive, settings.Menu.SearchActive.ToString());
    }

    public async Task UpdateApplicationUiManagementSettingsAsync(ThemeSettingsDto settings, UserIdentifier changerUser)
    {
        await this.SettingManager.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.DarkMode, settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.LayoutType, settings.Layout.LayoutType);
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Header.DesktopFixedHeader, settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Header.MobileFixedHeader, settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Header.MinimizeType, settings.Header.MinimizeDesktopHeaderType);
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.SearchActive, settings.Menu.SearchActive.ToString());
    }

    public async Task<ThemeSettingsDto> GetHostUiManagementSettings()
    {
        var theme = await this.SettingManager.GetSettingValueForApplicationAsync(AppSettings.UiManagement.Theme);

        return new ThemeSettingsDto
        {
            Theme = theme,
            Layout = new ThemeLayoutSettingsDto
            {
                LayoutType = await this.GetSettingValueForApplicationAsync(AppSettings.UiManagement.LayoutType),
                DarkMode = await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.DarkMode)
            },
            Header = new ThemeHeaderSettingsDto
            {
                MinimizeDesktopHeaderType = await this.GetSettingValueForApplicationAsync(AppSettings.UiManagement.Header.MinimizeType)
            },
            Menu = new ThemeMenuSettingsDto()
            {
                SearchActive = await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.SearchActive)
            }
        };
    }

    public async Task<ThemeSettingsDto> GetTenantUiCustomizationSettings(int tenantId)
    {
        var theme = await this.SettingManager.GetSettingValueForTenantAsync(AppSettings.UiManagement.Theme, tenantId);

        return new ThemeSettingsDto
        {
            Theme = theme,
            Layout = new ThemeLayoutSettingsDto
            {
                LayoutType = await this.GetSettingValueForTenantAsync(AppSettings.UiManagement.LayoutType, tenantId),
                DarkMode = await this.GetSettingValueForTenantAsync<bool>(AppSettings.UiManagement.DarkMode, tenantId)
            },
            Header = new ThemeHeaderSettingsDto
            {
                MinimizeDesktopHeaderType = await this.GetSettingValueForTenantAsync(AppSettings.UiManagement.Header.MinimizeType, tenantId)
            },
            Menu = new ThemeMenuSettingsDto()
            {
                SearchActive = await this.GetSettingValueForTenantAsync<bool>(AppSettings.UiManagement.SearchActive, tenantId)
            }
        };
    }
}