using System.Threading.Tasks;
using Abp;
using Abp.Configuration;
using RealtoCrm.Configuration;
using RealtoCrm.Configuration.Dto;
using RealtoCrm.UiCustomization;
using RealtoCrm.UiCustomization.Dto;

namespace RealtoCrm.Web.UiCustomization.Metronic;

public class Theme8UiCustomizer(ISettingManager settingManager) : UiThemeCustomizerBase(settingManager, AppConsts.Theme8), IUiCustomizer
{
    public async Task<UiCustomizationSettingsDto> GetUiSettings()
    {
        var settings = new UiCustomizationSettingsDto
        {
            BaseSettings = new ThemeSettingsDto
            {
                Layout = new ThemeLayoutSettingsDto
                {
                    LayoutType = await this.GetSettingValueAsync(AppSettings.UiManagement.LayoutType),
                    DarkMode = await this.GetSettingValueAsync<bool>(AppSettings.UiManagement.DarkMode)
                },
                Menu = new ThemeMenuSettingsDto()
                {
                    SearchActive = await this.GetSettingValueAsync<bool>(AppSettings.UiManagement.SearchActive)
                }
            }
        };

        settings.BaseSettings.Theme = this.ThemeName;
        settings.BaseSettings.Menu.Position = "tab";
        settings.BaseSettings.Menu.AsideSkin = "dark";
            
        settings.BaseSettings.SubHeader.SubheaderSize = 1;
        settings.BaseSettings.SubHeader.TitleStyle = "text-dark fw-bold my-2 me-5";
        settings.BaseSettings.SubHeader.ContainerStyle = "subheader py-0";
        settings.BaseSettings.SubHeader.SubContainerStyle = "py-0";
            
        settings.IsLeftMenuUsed = false;
        settings.IsTopMenuUsed = false;
        settings.IsTabMenuUsed = true;

        return settings;
    }

    public async Task UpdateUserUiManagementSettingsAsync(UserIdentifier user, ThemeSettingsDto settings)
    {
        await this.SettingManager.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.DarkMode, settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.LayoutType, settings.Layout.LayoutType);
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Header.DesktopFixedHeader, settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.Header.MobileFixedHeader, settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForUserAsync(user, AppSettings.UiManagement.SearchActive, settings.Menu.SearchActive.ToString());
    }

    public async Task UpdateTenantUiManagementSettingsAsync(int tenantId, ThemeSettingsDto settings, UserIdentifier changerUser)
    {
        await this.SettingManager.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.DarkMode, settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.LayoutType, settings.Layout.LayoutType);
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Header.DesktopFixedHeader, settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.Header.MobileFixedHeader, settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForTenantAsync(tenantId, AppSettings.UiManagement.SearchActive, settings.Menu.SearchActive.ToString());
            
        await this.ResetDarkModeSettingsAsync(changerUser);
    }

    public async Task UpdateApplicationUiManagementSettingsAsync(ThemeSettingsDto settings, UserIdentifier changerUser)
    {
        await this.SettingManager.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Theme, this.ThemeName);

        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.DarkMode, settings.Layout.DarkMode.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.LayoutType, settings.Layout.LayoutType);
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Header.DesktopFixedHeader, settings.Header.DesktopFixedHeader.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.Header.MobileFixedHeader, settings.Header.MobileFixedHeader.ToString());
        await this.ChangeSettingForApplicationAsync(AppSettings.UiManagement.SearchActive, settings.Menu.SearchActive.ToString());
            
        await this.ResetDarkModeSettingsAsync(changerUser);
    }

    public async Task<ThemeSettingsDto> GetHostUiManagementSettings()
    {
        var theme = await this.SettingManager.GetSettingValueForApplicationAsync(AppSettings.UiManagement.Theme);

        return new ThemeSettingsDto
        {
            Theme = theme,
            Layout = new ThemeLayoutSettingsDto
            {
                LayoutType = await this.GetSettingValueForApplicationAsync(AppSettings.UiManagement.LayoutType),
                DarkMode = await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.DarkMode)
            },
            Menu = new ThemeMenuSettingsDto()
            {
                SearchActive = await this.GetSettingValueForApplicationAsync<bool>(AppSettings.UiManagement.SearchActive)
            }
        };
    }

    public async Task<ThemeSettingsDto> GetTenantUiCustomizationSettings(int tenantId)
    {
        var theme = await this.SettingManager.GetSettingValueForTenantAsync(AppSettings.UiManagement.Theme, tenantId);

        return new ThemeSettingsDto
        {
            Theme = theme,
            Layout = new ThemeLayoutSettingsDto
            {
                LayoutType = await this.GetSettingValueForTenantAsync(AppSettings.UiManagement.LayoutType, tenantId),
                DarkMode = await this.GetSettingValueForTenantAsync<bool>(AppSettings.UiManagement.DarkMode, tenantId)
            },
            Menu = new ThemeMenuSettingsDto()
            {
                SearchActive = await this.GetSettingValueForTenantAsync<bool>(AppSettings.UiManagement.SearchActive, tenantId)
            }
        };
    }
}