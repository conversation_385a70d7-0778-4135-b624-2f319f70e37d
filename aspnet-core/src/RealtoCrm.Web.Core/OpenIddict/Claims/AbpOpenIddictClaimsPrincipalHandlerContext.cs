using System;
using System.Security.Claims;
using OpenIddict.Abstractions;

namespace RealtoCrm.Web.OpenIddict.Claims;

public class AbpOpenIddictClaimsPrincipalHandlerContext(IServiceProvider scopeServiceProvider, OpenIddictRequest openIddictRequest, ClaimsPrincipal principal)
{
    public IServiceProvider ScopeServiceProvider { get; } = scopeServiceProvider;

    public OpenIddictRequest OpenIddictRequest { get; } = openIddictRequest;

    public ClaimsPrincipal Principal { get;} = principal;
}